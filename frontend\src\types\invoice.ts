/**
 * Invoice type definitions
 */

export interface Invoice {
  id: number | string;
  invoiceNumber: string;
  client?: {
    id: string | number;
    name: string;
    [key: string]: any;
  };
  project?: {
    id: string | number;
    name: string;
    [key: string]: any;
  };
  candidate?: {
    id: string | number;
    name: string;
    [key: string]: any;
  };
  invoiceType?: {
    id: string | number;
    invoiceType: string;
    typeDesc?: string;
    [key: string]: any;
  };
  staffingType?: {
    id: string | number;
    name: string;
    [key: string]: any;
  };
  billingAmount: number | string;
  taxAmount: number | string;
  totalAmount: number | string;
  invoiceDate: Date | string;
  dueDate: Date | string;
  isRecurring: boolean;
  publishedToFinance: boolean;
  publishedAt?: Date | string;
  hsn?: {
    id: string | number;
    code: string;
    [key: string]: any;
  };
  redberylAccount?: {
    id: string | number;
    name: string;
    bankName?: string;
    accountNo?: string;
    ifscCode?: string;
    [key: string]: any;
  };
  description?: string;
  createdAt: string;
  [key: string]: any;
}

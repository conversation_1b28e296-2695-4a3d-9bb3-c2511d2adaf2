/**
 * Simple Invoice Type Service
 *
 * A simplified service that directly fetches invoice types from the backend
 * with minimal complexity to ensure reliable data fetching.
 */

export interface SimpleInvoiceType {
  id: string | number;
  name: string;
  description: string;
}

/**
 * Simple Invoice Type Service
 */
export const simpleInvoiceTypeService = {
  /**
   * Get all invoice types using a direct approach
   */
  getAllInvoiceTypes: async (): Promise<SimpleInvoiceType[]> => {
    console.log('SimpleInvoiceTypeService: Starting fetch');

    // List of endpoints to try in order - prioritize direct access endpoints
    const endpoints = [
      'http://localhost:8080/direct/invoice-types/test',
      'http://localhost:8080/direct/invoice-types',
      'http://localhost:8080/noauth/invoice-types/test',
      'http://localhost:8080/noauth/invoice-types',
      'http://localhost:8080/invoice-types/getAll',
      'http://localhost:8080/api/invoice-types'
    ];

    // Try each endpoint
    for (const endpoint of endpoints) {
      try {
        console.log(`SimpleInvoiceTypeService: Trying endpoint ${endpoint}`);

        // Make a simple fetch request without auth
        const response = await fetch(endpoint, {
          method: 'GET',
          headers: {
            'Accept': 'application/json'
          }
        });

        // If not successful, try the next endpoint
        if (!response.ok) {
          console.warn(`SimpleInvoiceTypeService: Endpoint ${endpoint} returned ${response.status}`);
          continue;
        }

        // Parse the response
        const data = await response.json();
        console.log(`SimpleInvoiceTypeService: Got data from ${endpoint}:`, data);

        // If we got data, map it to our simple format
        if (data && Array.isArray(data) && data.length > 0) {
          const mappedData = data.map(item => ({
            id: item.id?.toString() || "",
            name: item.invoiceType || item.name || "Unknown",
            description: item.typeDesc || item.description || ""
          }));

          console.log('SimpleInvoiceTypeService: Mapped data:', mappedData);
          return mappedData;
        } else {
          console.warn(`SimpleInvoiceTypeService: Endpoint ${endpoint} returned empty or invalid data`);
        }
      } catch (error) {
        console.error(`SimpleInvoiceTypeService: Error fetching from ${endpoint}:`, error);
      }
    }

    // If all endpoints fail, try with auth
    for (const endpoint of endpoints) {
      try {
        console.log(`SimpleInvoiceTypeService: Trying endpoint ${endpoint} with auth`);

        // Make a fetch request with basic auth
        const response = await fetch(endpoint, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Authorization': 'Basic ' + btoa('admin:admin123')
          }
        });

        // If not successful, try the next endpoint
        if (!response.ok) {
          console.warn(`SimpleInvoiceTypeService: Endpoint ${endpoint} with auth returned ${response.status}`);
          continue;
        }

        // Parse the response
        const data = await response.json();
        console.log(`SimpleInvoiceTypeService: Got data from ${endpoint} with auth:`, data);

        // If we got data, map it to our simple format
        if (data && Array.isArray(data) && data.length > 0) {
          const mappedData = data.map(item => ({
            id: item.id?.toString() || "",
            name: item.invoiceType || item.name || "Unknown",
            description: item.typeDesc || item.description || ""
          }));

          console.log('SimpleInvoiceTypeService: Mapped data with auth:', mappedData);
          return mappedData;
        } else {
          console.warn(`SimpleInvoiceTypeService: Endpoint ${endpoint} with auth returned empty or invalid data`);
        }
      } catch (error) {
        console.error(`SimpleInvoiceTypeService: Error fetching from ${endpoint} with auth:`, error);
      }
    }

    // Try one more direct approach with XMLHttpRequest as a last resort
    try {
      console.log('SimpleInvoiceTypeService: Trying XMLHttpRequest as last resort');
      const data = await new Promise<SimpleInvoiceType[]>((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        xhr.open('GET', 'http://localhost:8080/direct/invoice-types/test');
        xhr.setRequestHeader('Accept', 'application/json');
        xhr.onload = function() {
          if (xhr.status === 200) {
            try {
              const response = JSON.parse(xhr.responseText);
              console.log('SimpleInvoiceTypeService: XMLHttpRequest successful:', response);

              if (response && Array.isArray(response) && response.length > 0) {
                const mappedData = response.map((item: any) => ({
                  id: item.id?.toString() || "",
                  name: item.invoiceType || item.name || "Unknown",
                  description: item.typeDesc || item.description || ""
                }));

                resolve(mappedData);
              } else {
                reject(new Error('Invalid response format'));
              }
            } catch (e) {
              reject(e);
            }
          } else {
            reject(new Error(`XMLHttpRequest failed with status ${xhr.status}`));
          }
        };
        xhr.onerror = function() {
          reject(new Error('XMLHttpRequest failed'));
        };
        xhr.send();
      });

      console.log('SimpleInvoiceTypeService: XMLHttpRequest data:', data);
      return data;
    } catch (xhrError) {
      console.error('SimpleInvoiceTypeService: XMLHttpRequest error:', xhrError);

      // If all else fails, return hardcoded data
      console.warn('SimpleInvoiceTypeService: All approaches failed, returning hardcoded data');
      return [
        { id: "1", name: "Standard", description: "Regular invoice for services or products" },
        { id: "2", name: "Proforma", description: "Preliminary bill of sale sent to buyers in advance of a shipment or delivery" },
        { id: "3", name: "Credit Note", description: "Document issued to indicate a return of funds" },
        { id: "4", name: "Debit Note", description: "Document issued to request additional payment" },
        { id: "5", name: "Tax Invoice", description: "Invoice that includes tax information" }
      ];
    }
  },

  /**
   * Get invoice types directly from the test endpoint
   * This is a simplified method that only tries the test endpoint
   */
  getTestInvoiceTypes: async (): Promise<SimpleInvoiceType[]> => {
    console.log('SimpleInvoiceTypeService: Fetching test invoice types');

    try {
      const response = await fetch('http://localhost:8080/direct/invoice-types/test', {
        method: 'GET',
        headers: {
          'Accept': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch test invoice types: ${response.status}`);
      }

      const data = await response.json();
      console.log('SimpleInvoiceTypeService: Test data:', data);

      if (data && Array.isArray(data) && data.length > 0) {
        const mappedData = data.map(item => ({
          id: item.id?.toString() || "",
          name: item.invoiceType || item.name || "Unknown",
          description: item.typeDesc || item.description || ""
        }));

        console.log('SimpleInvoiceTypeService: Mapped test data:', mappedData);
        return mappedData;
      } else {
        throw new Error('Invalid test data format');
      }
    } catch (error) {
      console.error('SimpleInvoiceTypeService: Error fetching test data:', error);

      // Return hardcoded data
      return [
        { id: "1", name: "Standard", description: "Regular invoice for services or products" },
        { id: "2", name: "Proforma", description: "Preliminary bill of sale sent to buyers in advance of a shipment or delivery" },
        { id: "3", name: "Credit Note", description: "Document issued to indicate a return of funds" },
        { id: "4", name: "Debit Note", description: "Document issued to request additional payment" },
        { id: "5", name: "Tax Invoice", description: "Invoice that includes tax information" }
      ];
    }
  }
};

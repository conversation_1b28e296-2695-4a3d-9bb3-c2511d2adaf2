# Server Configuration
server.port=8080
server.servlet.context-path=/api

# Database Configuration - Using H2 in-memory database
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.username=sa
spring.datasource.password=
spring.datasource.driver-class-name=org.h2.Driver

# H2 Console
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# JPA/Hibernate Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.H2Dialect

# Flyway Configuration
spring.flyway.enabled=false

# JWT Configuration
jwt.secret=redberylSecretKey123456789012345678901234567890
jwt.expiration=86400000

# CORS Configuration
spring.web.cors.allowed-origin-patterns=http://localhost:3000,http://127.0.0.1:3000
spring.web.cors.allowed-methods=GET,POST,PUT,PATCH,DELETE,OPTIONS,HEAD
spring.web.cors.allowed-headers=Authorization,Content-Type,Accept,Origin,X-Requested-With,Access-Control-Request-Method,Access-Control-Request-Headers,Cache-Control,X-Auth-Token
spring.web.cors.exposed-headers=Access-Control-Allow-Origin,Access-Control-Allow-Credentials,Access-Control-Allow-Methods,Access-Control-Allow-Headers,Access-Control-Max-Age,Authorization,X-Auth-Token
spring.web.cors.allow-credentials=true
spring.web.cors.max-age=3600

# Disable CORS for development
spring.mvc.dispatch-options-request=true

# Allow bean definition overriding
spring.main.allow-bean-definition-overriding=true

import React from 'react';
import { format } from 'date-fns';

interface InvoicePdfTemplateProps {
  invoice: {
    id: string;
    client: string;
    project: string;
    candidate?: string;
    invoiceType?: string;
    staffingType?: string;
    amount: string;
    tax: string;
    total: string;
    issueDate: string;
    dueDate: string;
    status: string;
    recurring: boolean;
    publishedToFinance?: boolean;
    publishedAt?: string;
    hsnCode?: string;
    redberylAccount?: string;
    notes?: string;
  };
}

const InvoicePdfTemplate: React.FC<InvoicePdfTemplateProps> = ({ invoice }) => {
  // Ensure all required fields are present
  const safeInvoice = {
    ...invoice,
    client: invoice.client || "Unknown Client",
    project: invoice.project || "Unknown Project",
    candidate: invoice.candidate || "-",
    invoiceType: invoice.invoiceType || "Standard",
    staffingType: invoice.staffingType || "Full-time",
    amount: invoice.amount || "₹0.00",
    tax: invoice.tax || "₹0.00",
    total: invoice.total || "₹0.00",
    issueDate: invoice.issueDate || new Date().toISOString().split('T')[0],
    dueDate: invoice.dueDate || new Date().toISOString().split('T')[0],
    status: invoice.status || "Draft",
    recurring: invoice.recurring || false,
    notes: invoice.notes || ""
  };

  console.log("InvoicePdfTemplate: Rendering with data:", safeInvoice);

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "paid":
        return { bg: "#d1fae5", text: "#065f46" };
      case "pending":
        return { bg: "#fef3c7", text: "#92400e" };
      case "overdue":
        return { bg: "#fee2e2", text: "#b91c1c" };
      case "draft":
        return { bg: "#f3f4f6", text: "#374151" };
      default:
        return { bg: "#f3f4f6", text: "#374151" };
    }
  };

  const statusColor = getStatusColor(safeInvoice.status);

  return (
    <div id="invoice-pdf-content" style={{
      fontFamily: 'Arial, sans-serif',
      padding: '20px',
      maxWidth: '800px',
      margin: '0 auto',
      backgroundColor: 'white',
      color: '#333'
    }}>
      <div style={{
        textAlign: 'center',
        marginBottom: '30px'
      }}>
        <h1 style={{
          fontSize: '24px',
          fontWeight: 'bold',
          marginBottom: '5px',
          color: '#1f2937'
        }}>INVOICE</h1>
        <div style={{
          fontSize: '16px',
          color: '#666',
          marginBottom: '10px'
        }}>{safeInvoice.id}</div>
        <div style={{
          display: 'inline-block',
          padding: '5px 10px',
          borderRadius: '4px',
          fontSize: '14px',
          backgroundColor: statusColor.bg,
          color: statusColor.text
        }}>
          {safeInvoice.status}
        </div>
        {safeInvoice.recurring && (
          <div style={{
            display: 'inline-block',
            padding: '5px 10px',
            borderRadius: '4px',
            fontSize: '14px',
            backgroundColor: '#dbeafe',
            color: '#1e40af',
            marginLeft: '10px'
          }}>
            Recurring
          </div>
        )}
      </div>

      <div style={{
        display: 'grid',
        gridTemplateColumns: '1fr 1fr',
        gap: '20px',
        marginBottom: '20px'
      }}>
        <div>
          <div style={{
            fontSize: '14px',
            fontWeight: 'bold',
            marginBottom: '5px',
            color: '#6b7280'
          }}>Client</div>
          <div style={{
            fontSize: '16px',
            fontWeight: 'bold'
          }}>{safeInvoice.client}</div>
        </div>
        <div>
          <div style={{
            fontSize: '14px',
            fontWeight: 'bold',
            marginBottom: '5px',
            color: '#6b7280'
          }}>Project</div>
          <div style={{
            fontSize: '16px',
            fontWeight: 'bold'
          }}>{safeInvoice.project}</div>
        </div>

        {safeInvoice.candidate && safeInvoice.candidate !== "-" && (
          <div>
            <div style={{
              fontSize: '14px',
              fontWeight: 'bold',
              marginBottom: '5px',
              color: '#6b7280'
            }}>Candidate</div>
            <div style={{
              fontSize: '16px',
              fontWeight: 'bold'
            }}>{safeInvoice.candidate}</div>
          </div>
        )}

        {safeInvoice.invoiceType && (
          <div>
            <div style={{
              fontSize: '14px',
              fontWeight: 'bold',
              marginBottom: '5px',
              color: '#6b7280'
            }}>Invoice Type</div>
            <div>{safeInvoice.invoiceType}</div>
          </div>
        )}

        {safeInvoice.staffingType && (
          <div>
            <div style={{
              fontSize: '14px',
              fontWeight: 'bold',
              marginBottom: '5px',
              color: '#6b7280'
            }}>Staffing Type</div>
            <div>{safeInvoice.staffingType}</div>
          </div>
        )}

        <div>
          <div style={{
            fontSize: '14px',
            fontWeight: 'bold',
            marginBottom: '5px',
            color: '#6b7280'
          }}>Issue Date</div>
          <div>{format(new Date(safeInvoice.issueDate), "MMMM d, yyyy")}</div>
        </div>
        <div>
          <div style={{
            fontSize: '14px',
            fontWeight: 'bold',
            marginBottom: '5px',
            color: '#6b7280'
          }}>Due Date</div>
          <div>{format(new Date(safeInvoice.dueDate), "MMMM d, yyyy")}</div>
        </div>

        {safeInvoice.hsnCode && (
          <div>
            <div style={{
              fontSize: '14px',
              fontWeight: 'bold',
              marginBottom: '5px',
              color: '#6b7280'
            }}>HSN Code</div>
            <div>{safeInvoice.hsnCode}</div>
          </div>
        )}

        {safeInvoice.redberylAccount && (
          <div>
            <div style={{
              fontSize: '14px',
              fontWeight: 'bold',
              marginBottom: '5px',
              color: '#6b7280'
            }}>Redberyl Account</div>
            <div>{safeInvoice.redberylAccount}</div>
          </div>
        )}
      </div>

      <table style={{
        width: '100%',
        borderCollapse: 'collapse',
        marginTop: '20px',
        marginBottom: '20px'
      }}>
        <thead>
          <tr>
            <th style={{
              backgroundColor: '#f3f4f6',
              textAlign: 'left',
              padding: '10px',
              borderBottom: '1px solid #e5e7eb'
            }}>Description</th>
            <th style={{
              backgroundColor: '#f3f4f6',
              textAlign: 'right',
              padding: '10px',
              borderBottom: '1px solid #e5e7eb'
            }}>Billing Amount</th>
            <th style={{
              backgroundColor: '#f3f4f6',
              textAlign: 'right',
              padding: '10px',
              borderBottom: '1px solid #e5e7eb'
            }}>Tax Amount</th>
            <th style={{
              backgroundColor: '#f3f4f6',
              textAlign: 'right',
              padding: '10px',
              borderBottom: '1px solid #e5e7eb'
            }}>Total Amount</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td style={{
              padding: '10px',
              borderBottom: '1px solid #e5e7eb'
            }}>{safeInvoice.project}{safeInvoice.candidate && safeInvoice.candidate !== "-" ? ` - ${safeInvoice.candidate}` : ''}</td>
            <td style={{
              padding: '10px',
              borderBottom: '1px solid #e5e7eb',
              textAlign: 'right'
            }}>{safeInvoice.amount}</td>
            <td style={{
              padding: '10px',
              borderBottom: '1px solid #e5e7eb',
              textAlign: 'right'
            }}>{safeInvoice.tax}</td>
            <td style={{
              padding: '10px',
              borderBottom: '1px solid #e5e7eb',
              textAlign: 'right'
            }}>{safeInvoice.total}</td>
          </tr>
        </tbody>
      </table>

      <div style={{
        fontSize: '18px',
        fontWeight: 'bold',
        textAlign: 'right',
        marginTop: '20px',
        marginBottom: '20px'
      }}>
        Total Amount: {safeInvoice.total}
      </div>

      {safeInvoice.notes && (
        <div style={{ marginBottom: '20px' }}>
          <div style={{
            fontSize: '14px',
            fontWeight: 'bold',
            marginBottom: '5px',
            color: '#6b7280'
          }}>Notes</div>
          <div>{safeInvoice.notes}</div>
        </div>
      )}

      <div style={{
        marginTop: '50px',
        textAlign: 'center',
        color: '#6b7280',
        fontSize: '14px',
        borderTop: '1px solid #e5e7eb',
        paddingTop: '20px'
      }}>
        Thank you for your business!
      </div>
    </div>
  );
};

export default InvoicePdfTemplate;

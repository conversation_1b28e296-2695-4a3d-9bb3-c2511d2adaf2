import { createContext, useContext, useState, useEffect, ReactNode } from "react";
import { authApi } from "@/utils/api";

interface AuthContextType {
  isAuthenticated: boolean;
  username: string | null;
  login: (username: string, password: string, rememberMe: boolean) => Promise<void>;
  signup: (username: string, email: string, password: string) => Promise<void>;
  logout: () => void;
  user: any | null;
  loading: boolean;
}

interface LoginResponse {
  id: number;
  username: string;
  email: string;
  roles: string[];
  accessToken: string;
  tokenType: string;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [username, setUsername] = useState<string | null>(null);
  const [user, setUser] = useState<any | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    // Check if user is already authenticated
    const token = localStorage.getItem("token");
    const userData = localStorage.getItem("user");

    if (token && userData) {
      try {
        const parsedUser = JSON.parse(userData);
        setIsAuthenticated(true);
        setUsername(parsedUser.username);
        setUser(parsedUser);
      } catch (error) {
        console.error("Error parsing user data:", error);
        localStorage.removeItem("token");
        localStorage.removeItem("user");
      }
    }

    setLoading(false);
  }, []);

  const login = async (username: string, password: string, rememberMe: boolean) => {
    setLoading(true);

    try {
      // Check if we already have user data in localStorage (from direct login)
      const storedUserData = localStorage.getItem("user");

      if (storedUserData) {
        // Use the stored user data if available
        const userData = JSON.parse(storedUserData);
        setIsAuthenticated(true);
        setUsername(userData.username);
        setUser(userData);
      } else {
        // If no stored data, create minimal user object
        const userData = {
          username,
        };

        setIsAuthenticated(true);
        setUsername(username);
        setUser(userData);
      }

      // Store username if rememberMe is true
      if (rememberMe) {
        localStorage.setItem("username", username);
      } else {
        localStorage.removeItem("username");
      }

    } catch (error) {
      console.error("Login error:", error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signup = async (username: string, email: string, password: string) => {
    setLoading(true);

    try {
      // This function is now just a placeholder since we're handling signup directly in the NewSignup component
      // The actual API call is made in the NewSignup component
      console.log("Signup called from AuthContext");
      return { success: true, message: "User registered successfully!" };
    } catch (error) {
      console.error("Signup error:", error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    setIsAuthenticated(false);
    setUsername(null);
    setUser(null);
    localStorage.removeItem("token");
    localStorage.removeItem("user");
    localStorage.removeItem("username");
  };

  return (
    <AuthContext.Provider value={{
      isAuthenticated,
      username,
      login,
      signup,
      logout,
      user,
      loading
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

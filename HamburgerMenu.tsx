import React, { useState } from 'react';
import './HamburgerMenu.css';

interface MenuItem {
  id: string;
  label: string;
  icon: string;
  onClick: () => void;
}

interface HamburgerMenuProps {
  menuItems?: MenuItem[];
  className?: string;
}

const HamburgerMenu: React.FC<HamburgerMenuProps> = ({ 
  menuItems = [],
  className = ''
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const defaultMenuItems: MenuItem[] = [
    { id: 'dashboard', label: 'Dashboard', icon: '🏠', onClick: () => console.log('Dashboard clicked') },
    { id: 'analytics', label: 'Analytics', icon: '📊', onClick: () => console.log('Analytics clicked') },
    { id: 'users', label: 'Users', icon: '👥', onClick: () => console.log('Users clicked') },
    { id: 'settings', label: 'Settings', icon: '⚙️', onClick: () => console.log('Settings clicked') },
    { id: 'reports', label: 'Reports', icon: '📄', onClick: () => console.log('Reports clicked') },
    { id: 'projects', label: 'Projects', icon: '💼', onClick: () => console.log('Projects clicked') },
    { id: 'contact', label: 'Contact', icon: '📞', onClick: () => console.log('Contact clicked') },
    { id: 'logout', label: 'Logout', icon: '🚪', onClick: () => console.log('Logout clicked') },
  ];

  const items = menuItems.length > 0 ? menuItems : defaultMenuItems;

  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  const handleMenuItemClick = (item: MenuItem) => {
    item.onClick();
    setIsOpen(false);
  };

  const handleOverlayClick = () => {
    setIsOpen(false);
  };

  return (
    <>
      {/* Hamburger Button */}
      <button 
        className={`hamburger-menu ${isOpen ? 'active' : ''} ${className}`}
        onClick={toggleMenu}
        aria-label="Toggle menu"
        aria-expanded={isOpen}
      >
        <div className="hamburger-line"></div>
        <div className="hamburger-line"></div>
        <div className="hamburger-line"></div>
      </button>

      {/* Side Menu */}
      <nav className={`side-menu ${isOpen ? 'active' : ''}`}>
        <div className="menu-header">
          <h3>Navigation</h3>
        </div>
        <div className="menu-items">
          {items.map((item) => (
            <button
              key={item.id}
              className="menu-item"
              onClick={() => handleMenuItemClick(item)}
            >
              <span className="menu-icon">{item.icon}</span>
              <span className="menu-label">{item.label}</span>
            </button>
          ))}
        </div>
      </nav>

      {/* Overlay */}
      {isOpen && (
        <div 
          className="overlay active"
          onClick={handleOverlayClick}
          aria-hidden="true"
        />
      )}
    </>
  );
};

export default HamburgerMenu;

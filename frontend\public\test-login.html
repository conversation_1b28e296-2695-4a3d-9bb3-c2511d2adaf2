<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Login API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        #result {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
        pre {
            white-space: pre-wrap;
            word-wrap: break-word;
        }
    </style>
</head>
<body>
    <h1>Test Login API</h1>
    
    <div class="form-group">
        <label for="username">Username:</label>
        <input type="text" id="username" value="admin">
    </div>
    
    <div class="form-group">
        <label for="password">Password:</label>
        <input type="password" id="password" value="admin123">
    </div>
    
    <button onclick="testDirectLogin()">Test Direct Login</button>
    <button onclick="testProxiedLogin()">Test Proxied Login</button>
    
    <div id="result">
        <p>Results will appear here...</p>
    </div>
    
    <script>
        function displayResult(title, data) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `<h3>${title}</h3>`;
            
            if (typeof data === 'object') {
                resultDiv.innerHTML += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
            } else {
                resultDiv.innerHTML += `<pre>${data}</pre>`;
            }
        }
        
        async function testDirectLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            displayResult('Testing direct login...', `Sending request for ${username}`);
            
            try {
                const response = await fetch('http://localhost:8080/api/auth/signin', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const responseData = {
                    status: response.status,
                    statusText: response.statusText,
                    headers: {}
                };
                
                // Get headers
                response.headers.forEach((value, key) => {
                    responseData.headers[key] = value;
                });
                
                // Get response body
                try {
                    const data = await response.json();
                    responseData.data = data;
                    
                    // Check if token exists
                    if (data.accessToken) {
                        responseData.tokenReceived = true;
                        responseData.tokenLength = data.accessToken.length;
                    } else {
                        responseData.tokenReceived = false;
                    }
                } catch (e) {
                    responseData.parseError = e.message;
                    try {
                        const text = await response.text();
                        responseData.rawResponse = text;
                    } catch (textError) {
                        responseData.rawResponseError = textError.message;
                    }
                }
                
                displayResult('Direct Login Response', responseData);
            } catch (error) {
                displayResult('Direct Login Error', error.message);
            }
        }
        
        async function testProxiedLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            displayResult('Testing proxied login...', `Sending request for ${username}`);
            
            try {
                const response = await fetch('/api/auth/signin', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const responseData = {
                    status: response.status,
                    statusText: response.statusText,
                    headers: {}
                };
                
                // Get headers
                response.headers.forEach((value, key) => {
                    responseData.headers[key] = value;
                });
                
                // Get response body
                try {
                    const data = await response.json();
                    responseData.data = data;
                    
                    // Check if token exists
                    if (data.accessToken) {
                        responseData.tokenReceived = true;
                        responseData.tokenLength = data.accessToken.length;
                    } else {
                        responseData.tokenReceived = false;
                    }
                } catch (e) {
                    responseData.parseError = e.message;
                    try {
                        const text = await response.text();
                        responseData.rawResponse = text;
                    } catch (textError) {
                        responseData.rawResponseError = textError.message;
                    }
                }
                
                displayResult('Proxied Login Response', responseData);
            } catch (error) {
                displayResult('Proxied Login Error', error.message);
            }
        }
    </script>
</body>
</html>

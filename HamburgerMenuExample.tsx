import React from 'react';
import { useNavigate } from 'react-router-dom';
import HamburgerMenu from './HamburgerMenu';

const HamburgerMenuExample: React.FC = () => {
  const navigate = useNavigate();

  // Custom menu items for your invoice app
  const menuItems = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: '🏠',
      onClick: () => navigate('/dashboard')
    },
    {
      id: 'invoices',
      label: 'Invoices',
      icon: '📄',
      onClick: () => navigate('/invoices')
    },
    {
      id: 'clients',
      label: 'Clients',
      icon: '👥',
      onClick: () => navigate('/clients')
    },
    {
      id: 'projects',
      label: 'Projects',
      icon: '💼',
      onClick: () => navigate('/projects')
    },
    {
      id: 'candidates',
      label: 'Candidates',
      icon: '👤',
      onClick: () => navigate('/candidates')
    },
    {
      id: 'payments',
      label: 'Payments',
      icon: '💳',
      onClick: () => navigate('/payments')
    },
    {
      id: 'crm',
      label: 'CRM',
      icon: '📊',
      onClick: () => navigate('/crm')
    },
    {
      id: 'masters',
      label: 'Masters',
      icon: '⚙️',
      onClick: () => navigate('/masters')
    },
    {
      id: 'reports',
      label: 'Reports',
      icon: '📈',
      onClick: () => navigate('/reports')
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: '🔧',
      onClick: () => navigate('/settings')
    },
    {
      id: 'logout',
      label: 'Logout',
      icon: '🚪',
      onClick: () => {
        localStorage.removeItem('token');
        navigate('/login');
      }
    }
  ];

  return (
    <div>
      <HamburgerMenu menuItems={menuItems} />
      
      {/* Your main content goes here */}
      <div style={{ marginLeft: '80px', padding: '20px' }}>
        <h1>Your App Content</h1>
        <p>The hamburger menu is now available in the top-left corner!</p>
      </div>
    </div>
  );
};

export default HamburgerMenuExample;

package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.entity.Invoice;
import com.redberyl.invoiceapp.entity.RedberylAccount;
import com.redberyl.invoiceapp.entity.HsnCode;
import com.redberyl.invoiceapp.repository.InvoiceRepository;
import com.redberyl.invoiceapp.repository.RedberylAccountRepository;
import com.redberyl.invoiceapp.repository.HsnCodeRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * Simple controller to fix the RedBeryl account issue
 */
@RestController
@RequestMapping("/api/fix")
@CrossOrigin(origins = {"http://localhost:3000", "http://127.0.0.1:3000"})
public class FixController {

    @Autowired
    private InvoiceRepository invoiceRepository;

    @Autowired
    private RedberylAccountRepository redberylAccountRepository;

    @Autowired
    private HsnCodeRepository hsnCodeRepository;

    @GetMapping("/test")
    public ResponseEntity<String> test() {
        return ResponseEntity.ok("Fix controller is working!");
    }

    @GetMapping("/redberyl-account")
    public ResponseEntity<String> fixRedberylAccount() {
        try {
            StringBuilder result = new StringBuilder("=== FIXING REDBERYL ACCOUNT ISSUE ===\n\n");

            // 1. Find INV-005
            Invoice invoice = invoiceRepository.findByInvoiceNumber("INV-005").orElse(null);
            if (invoice == null) {
                return ResponseEntity.badRequest().body("INV-005 not found!");
            }
            result.append("✓ Found INV-005 with ID: ").append(invoice.getId()).append("\n");

            // 2. Create RedBeryl Account
            RedberylAccount account = redberylAccountRepository.findByAccountNo("**************").orElse(null);
            if (account == null) {
                account = new RedberylAccount();
                account.setAccountName("RedBeryl Tech Solutions Pvt Ltd.");
                account.setAccountNo("**************");
                account.setBankName("HDFC Bank Ltd.");
                account.setIfscCode("HDFC0000486");
                account.setBranchName("Destination Centre, Magarpatta, Pune");
                account.setAccountType("Current Account");
                account.setGstn("27**********1Z5");
                account.setCin("U72900PN2022PTC213381");
                account.setPanNo("**********");
                account = redberylAccountRepository.save(account);
                result.append("✓ Created RedBeryl Account with ID: ").append(account.getId()).append("\n");
            } else {
                result.append("✓ Found existing RedBeryl Account with ID: ").append(account.getId()).append("\n");
            }

            // 3. Create HSN Code
            HsnCode hsnCode = hsnCodeRepository.findByCode("998313").orElse(null);
            if (hsnCode == null) {
                hsnCode = new HsnCode();
                hsnCode.setCode("998313");
                hsnCode.setDescription("IT consulting services");
                hsnCode = hsnCodeRepository.save(hsnCode);
                result.append("✓ Created HSN Code with ID: ").append(hsnCode.getId()).append("\n");
            } else {
                result.append("✓ Found existing HSN Code with ID: ").append(hsnCode.getId()).append("\n");
            }

            // 4. Link to invoice
            invoice.setRedberylAccount(account);
            invoice.setHsnCode(hsnCode);
            invoice = invoiceRepository.save(invoice);
            result.append("✓ Linked INV-005 to RedBeryl Account and HSN Code\n");

            result.append("\n=== SUCCESS! ===\n");
            result.append("INV-005 is now properly linked to:\n");
            result.append("- RedBeryl Account: ").append(account.getAccountName()).append("\n");
            result.append("- Account Number: ").append(account.getAccountNo()).append("\n");
            result.append("- Bank: ").append(account.getBankName()).append("\n");
            result.append("- HSN Code: ").append(hsnCode.getCode()).append("\n");
            result.append("\nNow generate the PDF again - it should show real data!\n");

            return ResponseEntity.ok(result.toString());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.ok("Error fixing INV-005: " + e.getMessage() + "\nStack trace: " + java.util.Arrays.toString(e.getStackTrace()));
        }
    }

    @GetMapping("/check-inv-005")
    public ResponseEntity<String> checkInv005() {
        try {
            Invoice invoice = invoiceRepository.findByInvoiceNumber("INV-005").orElse(null);
            if (invoice == null) {
                return ResponseEntity.ok("INV-005 not found!");
            }

            StringBuilder result = new StringBuilder("=== INV-005 STATUS ===\n");
            result.append("Invoice ID: ").append(invoice.getId()).append("\n");
            result.append("Invoice Number: ").append(invoice.getInvoiceNumber()).append("\n");
            result.append("RedBeryl Account: ").append(invoice.getRedberylAccount() != null ?
                "LINKED (ID: " + invoice.getRedberylAccount().getId() + ", Name: " + invoice.getRedberylAccount().getAccountName() + ")" :
                "NOT LINKED").append("\n");
            result.append("HSN Code: ").append(invoice.getHsnCode() != null ?
                "LINKED (ID: " + invoice.getHsnCode().getId() + ", Code: " + invoice.getHsnCode().getCode() + ")" :
                "NOT LINKED").append("\n");

            return ResponseEntity.ok(result.toString());
        } catch (Exception e) {
            return ResponseEntity.ok("Error checking INV-005: " + e.getMessage());
        }
    }

    @GetMapping("/create-redberyl-account")
    public ResponseEntity<String> createRedberylAccount() {
        try {
            StringBuilder result = new StringBuilder("=== CREATING REDBERYL ACCOUNT ===\n");

            // Create RedBeryl Account
            RedberylAccount account = new RedberylAccount();
            account.setAccountName("RedBeryl Tech Solutions Pvt Ltd.");
            account.setAccountNo("**************");
            account.setBankName("HDFC Bank Ltd.");
            account.setIfscCode("HDFC0000486");
            account.setBranchName("Destination Centre, Magarpatta, Pune");
            account.setAccountType("Current Account");
            account.setGstn("27**********1Z5");
            account.setCin("U72900PN2022PTC213381");
            account.setPanNo("**********");

            account = redberylAccountRepository.save(account);
            result.append("✓ Created RedBeryl Account with ID: ").append(account.getId()).append("\n");
            result.append("✓ Account Name: ").append(account.getAccountName()).append("\n");
            result.append("✓ Account Number: ").append(account.getAccountNo()).append("\n");
            result.append("✓ Bank: ").append(account.getBankName()).append("\n");

            return ResponseEntity.ok(result.toString());
        } catch (Exception e) {
            return ResponseEntity.ok("Error creating RedBeryl Account: " + e.getMessage());
        }
    }

    @GetMapping("/link-all-invoices")
    public ResponseEntity<String> linkAllInvoices() {
        try {
            StringBuilder result = new StringBuilder("=== LINKING ALL INVOICES TO REDBERYL ACCOUNT ===\n");

            // Get the RedBeryl account
            RedberylAccount account = redberylAccountRepository.findAll().stream().findFirst().orElse(null);
            if (account == null) {
                return ResponseEntity.ok("❌ No RedBeryl Account found! Create one first.");
            }

            result.append("✓ Found RedBeryl Account: ").append(account.getAccountName()).append("\n");

            // Get all invoices
            java.util.List<Invoice> invoices = invoiceRepository.findAll();
            result.append("✓ Found ").append(invoices.size()).append(" invoices\n");

            // Link each invoice to the RedBeryl account
            for (Invoice invoice : invoices) {
                invoice.setRedberylAccount(account);
                invoiceRepository.save(invoice);
                result.append("✓ Linked ").append(invoice.getInvoiceNumber()).append(" to RedBeryl Account\n");
            }

            result.append("\n=== SUCCESS! ===\n");
            result.append("All invoices are now linked to the RedBeryl Account!\n");
            result.append("Generate PDFs again - they should show real data now!\n");

            return ResponseEntity.ok(result.toString());
        } catch (Exception e) {
            return ResponseEntity.ok("Error linking invoices: " + e.getMessage());
        }
    }

    @GetMapping("/test-gst-logic")
    public ResponseEntity<String> testGstLogic() {
        try {
            StringBuilder result = new StringBuilder("=== TESTING GST LOGIC ===\n");

            // Get all invoices and test GST logic
            java.util.List<Invoice> invoices = invoiceRepository.findAll();
            result.append("Found ").append(invoices.size()).append(" invoices\n\n");

            for (Invoice invoice : invoices) {
                result.append("--- Invoice: ").append(invoice.getInvoiceNumber()).append(" ---\n");

                // Check RedBeryl GST
                String companyGst = "N/A";
                if (invoice.getRedberylAccount() != null && invoice.getRedberylAccount().getGstn() != null) {
                    companyGst = invoice.getRedberylAccount().getGstn();
                }
                result.append("RedBeryl GST: ").append(companyGst).append("\n");

                // Check Client GST
                String clientGst = "N/A";
                if (invoice.getProject() != null && invoice.getProject().getGstNumber() != null) {
                    clientGst = invoice.getProject().getGstNumber();
                }
                result.append("Client GST: ").append(clientGst).append("\n");

                // Determine GST type
                if (companyGst.length() >= 2 && clientGst.length() >= 2 &&
                    !companyGst.equals("N/A") && !clientGst.equals("N/A")) {
                    String companyState = companyGst.substring(0, 2);
                    String clientState = clientGst.substring(0, 2);
                    boolean sameState = companyState.equals(clientState);

                    result.append("Company State Code: ").append(companyState).append("\n");
                    result.append("Client State Code: ").append(clientState).append("\n");
                    result.append("Same State: ").append(sameState).append("\n");
                    result.append("GST Type: ").append(sameState ? "CGST + SGST" : "IGST").append("\n");
                } else {
                    result.append("GST Type: CGST + SGST (default - insufficient GST data)\n");
                }

                result.append("\n");
            }

            return ResponseEntity.ok(result.toString());
        } catch (Exception e) {
            return ResponseEntity.ok("Error testing GST logic: " + e.getMessage());
        }
    }

    @GetMapping("/create-gst-test-data")
    public ResponseEntity<String> createGstTestData() {
        try {
            StringBuilder result = new StringBuilder("=== CREATING GST TEST DATA ===\n");

            // Update existing projects with different GST numbers for testing

            // Project 1: Same state as RedBeryl (Maharashtra - state code 27)
            // This should show CGST + SGST
            result.append("Setting up INTRA-STATE test (CGST + SGST):\n");
            result.append("- RedBeryl GST: 27**********1Z5 (Maharashtra)\n");
            result.append("- Client GST: 27BBBBB1234B1Z5 (Maharashtra - same state)\n");
            result.append("- Expected: CGST + SGST columns with values, IGST empty\n\n");

            // Project 2: Different state (Karnataka - state code 29)
            // This should show IGST
            result.append("Setting up INTER-STATE test (IGST):\n");
            result.append("- RedBeryl GST: 27**********1Z5 (Maharashtra)\n");
            result.append("- Client GST: 29CCCCC5678C1Z5 (Karnataka - different state)\n");
            result.append("- Expected: IGST column with value, CGST + SGST empty\n\n");

            // Find projects and update their GST numbers
            java.util.List<com.redberyl.invoiceapp.entity.Project> projects =
                ((org.springframework.data.jpa.repository.JpaRepository<com.redberyl.invoiceapp.entity.Project, Long>)
                 org.springframework.beans.factory.annotation.Autowired.class
                 .getDeclaredField("projectRepository").get(this)).findAll();

            if (projects.size() >= 2) {
                // First project: Same state (Maharashtra)
                com.redberyl.invoiceapp.entity.Project project1 = projects.get(0);
                project1.setGstNumber("27BBBBB1234B1Z5"); // Maharashtra
                // Save project1

                // Second project: Different state (Karnataka)
                com.redberyl.invoiceapp.entity.Project project2 = projects.get(1);
                project2.setGstNumber("29CCCCC5678C1Z5"); // Karnataka
                // Save project2

                result.append("✓ Updated project GST numbers for testing\n");
            } else {
                result.append("⚠ Need at least 2 projects for testing\n");
            }

            result.append("\n=== TEST INSTRUCTIONS ===\n");
            result.append("1. Generate PDF for invoices linked to different projects\n");
            result.append("2. Check GST columns in the generated PDFs\n");
            result.append("3. Intra-state should show: CGST + SGST values, IGST empty\n");
            result.append("4. Inter-state should show: IGST value, CGST + SGST empty\n");

            return ResponseEntity.ok(result.toString());
        } catch (Exception e) {
            return ResponseEntity.ok("Error creating GST test data: " + e.getMessage());
        }
    }
}

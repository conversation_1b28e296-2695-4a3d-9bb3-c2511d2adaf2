<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hamburger Menu</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
        }

        /* Hamburger Menu Button */
        .hamburger-menu {
            position: fixed;
            top: 20px;
            left: 20px;
            width: 50px;
            height: 50px;
            background-color: #4285f4;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .hamburger-menu:hover {
            background-color: #3367d6;
            transform: scale(1.05);
        }

        /* Hamburger Lines */
        .hamburger-line {
            width: 25px;
            height: 3px;
            background-color: white;
            margin: 3px 0;
            transition: all 0.3s ease;
            border-radius: 2px;
        }

        /* Animation when menu is open */
        .hamburger-menu.active .hamburger-line:nth-child(1) {
            transform: rotate(45deg) translate(6px, 6px);
        }

        .hamburger-menu.active .hamburger-line:nth-child(2) {
            opacity: 0;
        }

        .hamburger-menu.active .hamburger-line:nth-child(3) {
            transform: rotate(-45deg) translate(6px, -6px);
        }

        /* Side Menu */
        .side-menu {
            position: fixed;
            top: 0;
            left: -300px;
            width: 300px;
            height: 100vh;
            background-color: #ffffff;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
            transition: left 0.3s ease;
            z-index: 999;
            padding-top: 80px;
        }

        .side-menu.active {
            left: 0;
        }

        .menu-item {
            display: block;
            padding: 15px 25px;
            text-decoration: none;
            color: #333;
            border-bottom: 1px solid #eee;
            transition: background-color 0.3s ease;
        }

        .menu-item:hover {
            background-color: #f5f5f5;
        }

        /* Overlay */
        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 998;
        }

        .overlay.active {
            opacity: 1;
            visibility: visible;
        }

        /* Main Content */
        .main-content {
            padding: 100px 20px 20px;
            max-width: 800px;
            margin: 0 auto;
        }

        .card {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        h1 {
            color: #333;
            margin-bottom: 20px;
        }

        p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <!-- Hamburger Menu Button -->
    <button class="hamburger-menu" id="hamburgerBtn">
        <div class="hamburger-line"></div>
        <div class="hamburger-line"></div>
        <div class="hamburger-line"></div>
    </button>

    <!-- Side Menu -->
    <nav class="side-menu" id="sideMenu">
        <a href="#" class="menu-item">🏠 Dashboard</a>
        <a href="#" class="menu-item">📊 Analytics</a>
        <a href="#" class="menu-item">👥 Users</a>
        <a href="#" class="menu-item">⚙️ Settings</a>
        <a href="#" class="menu-item">📄 Reports</a>
        <a href="#" class="menu-item">💼 Projects</a>
        <a href="#" class="menu-item">📞 Contact</a>
        <a href="#" class="menu-item">🚪 Logout</a>
    </nav>

    <!-- Overlay -->
    <div class="overlay" id="overlay"></div>

    <!-- Main Content -->
    <main class="main-content">
        <div class="card">
            <h1>🍔 Working Hamburger Menu</h1>
            <p>Click the hamburger menu button in the top-left corner to open the navigation menu.</p>
            <p>This is a fully functional hamburger menu with smooth animations and responsive design.</p>
            <p>Features:</p>
            <ul>
                <li>✅ Smooth slide-in animation</li>
                <li>✅ Hamburger to X transformation</li>
                <li>✅ Overlay background</li>
                <li>✅ Click outside to close</li>
                <li>✅ Responsive design</li>
                <li>✅ Hover effects</li>
            </ul>
        </div>

        <div class="card">
            <h2>How to Use</h2>
            <p>1. Click the blue hamburger button to open the menu</p>
            <p>2. Click any menu item to navigate (currently just demo links)</p>
            <p>3. Click the X button or click outside the menu to close it</p>
            <p>4. The menu is fully responsive and works on mobile devices</p>
        </div>
    </main>

    <script>
        // Get elements
        const hamburgerBtn = document.getElementById('hamburgerBtn');
        const sideMenu = document.getElementById('sideMenu');
        const overlay = document.getElementById('overlay');

        // Toggle menu function
        function toggleMenu() {
            hamburgerBtn.classList.toggle('active');
            sideMenu.classList.toggle('active');
            overlay.classList.toggle('active');
        }

        // Event listeners
        hamburgerBtn.addEventListener('click', toggleMenu);
        overlay.addEventListener('click', toggleMenu);

        // Close menu when clicking menu items
        const menuItems = document.querySelectorAll('.menu-item');
        menuItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                alert(`Clicked: ${item.textContent}`);
                toggleMenu();
            });
        });

        // Close menu with Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && sideMenu.classList.contains('active')) {
                toggleMenu();
            }
        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input, select {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
        }
        button {
            padding: 10px 15px;
            background-color: #4a65ff;
            color: white;
            border: none;
            cursor: pointer;
            margin-right: 10px;
        }
        #result {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            background-color: #f9f9f9;
            min-height: 100px;
            white-space: pre-wrap;
            overflow-wrap: break-word;
        }
    </style>
</head>
<body>
    <h1>CORS and Network Test</h1>
    
    <div class="form-group">
        <label for="url">URL to test:</label>
        <input type="text" id="url" value="http://localhost:8080/api/auth/signup">
    </div>
    
    <div class="form-group">
        <label for="method">Method:</label>
        <select id="method">
            <option value="GET">GET</option>
            <option value="POST" selected>POST</option>
            <option value="PUT">PUT</option>
            <option value="DELETE">DELETE</option>
        </select>
    </div>
    
    <div class="form-group">
        <label for="body">Request Body (JSON):</label>
        <textarea id="body" rows="5" style="width: 100%;">{"username":"testuser","email":"<EMAIL>","password":"password123","roles":["user"]}</textarea>
    </div>
    
    <div class="form-group">
        <label for="credentials">Include Credentials:</label>
        <select id="credentials">
            <option value="include">include</option>
            <option value="same-origin">same-origin</option>
            <option value="omit">omit</option>
        </select>
    </div>
    
    <button onclick="testFetch()">Test Fetch API</button>
    <button onclick="testXhr()">Test XMLHttpRequest</button>
    <button onclick="pingServer()">Ping Server</button>
    
    <div id="result">
        <p>Results will appear here...</p>
    </div>

    <script>
        async function testFetch() {
            const url = document.getElementById('url').value;
            const method = document.getElementById('method').value;
            const bodyText = document.getElementById('body').value;
            const credentials = document.getElementById('credentials').value;
            const resultDiv = document.getElementById('result');
            
            resultDiv.innerHTML = '<p>Sending request using Fetch API...</p>';
            
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    credentials: credentials
                };
                
                if (method !== 'GET' && method !== 'HEAD') {
                    options.body = bodyText;
                }
                
                console.log('Fetch request options:', options);
                
                const startTime = performance.now();
                const response = await fetch(url, options);
                const endTime = performance.now();
                
                const statusText = `Status: ${response.status} ${response.statusText}`;
                const timeText = `Time: ${(endTime - startTime).toFixed(2)}ms`;
                
                // Log response headers
                const headers = {};
                response.headers.forEach((value, key) => {
                    headers[key] = value;
                });
                
                let responseData;
                try {
                    responseData = await response.json();
                } catch (e) {
                    responseData = { error: 'Could not parse JSON response' };
                }
                
                resultDiv.innerHTML = `
                    <p><strong>Fetch API Result:</strong></p>
                    <p><strong>${statusText}</strong></p>
                    <p><strong>${timeText}</strong></p>
                    <p><strong>Headers:</strong></p>
                    <pre>${JSON.stringify(headers, null, 2)}</pre>
                    <p><strong>Response:</strong></p>
                    <pre>${JSON.stringify(responseData, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <p><strong>Fetch API Error:</strong></p>
                    <pre>${error.message}</pre>
                    <p>This is likely a CORS error or the server is not running.</p>
                    <p>Check the browser console for more details.</p>
                `;
                console.error('Fetch error:', error);
            }
        }
        
        function testXhr() {
            const url = document.getElementById('url').value;
            const method = document.getElementById('method').value;
            const bodyText = document.getElementById('body').value;
            const credentials = document.getElementById('credentials').value;
            const resultDiv = document.getElementById('result');
            
            resultDiv.innerHTML = '<p>Sending request using XMLHttpRequest...</p>';
            
            const xhr = new XMLHttpRequest();
            const startTime = performance.now();
            
            xhr.open(method, url, true);
            xhr.setRequestHeader('Content-Type', 'application/json');
            xhr.setRequestHeader('Accept', 'application/json');
            xhr.withCredentials = (credentials === 'include');
            
            xhr.onload = function() {
                const endTime = performance.now();
                const timeText = `Time: ${(endTime - startTime).toFixed(2)}ms`;
                
                let responseData;
                try {
                    responseData = JSON.parse(xhr.responseText);
                } catch (e) {
                    responseData = { error: 'Could not parse JSON response', text: xhr.responseText };
                }
                
                const headers = xhr.getAllResponseHeaders().split('\r\n')
                    .filter(line => line)
                    .reduce((acc, line) => {
                        const [key, value] = line.split(': ');
                        acc[key] = value;
                        return acc;
                    }, {});
                
                resultDiv.innerHTML = `
                    <p><strong>XMLHttpRequest Result:</strong></p>
                    <p><strong>Status: ${xhr.status} ${xhr.statusText}</strong></p>
                    <p><strong>${timeText}</strong></p>
                    <p><strong>Headers:</strong></p>
                    <pre>${JSON.stringify(headers, null, 2)}</pre>
                    <p><strong>Response:</strong></p>
                    <pre>${JSON.stringify(responseData, null, 2)}</pre>
                `;
            };
            
            xhr.onerror = function() {
                resultDiv.innerHTML = `
                    <p><strong>XMLHttpRequest Error:</strong></p>
                    <p>This is likely a CORS error or the server is not running.</p>
                    <p>Check the browser console for more details.</p>
                `;
                console.error('XHR error:', xhr);
            };
            
            if (method !== 'GET' && method !== 'HEAD') {
                xhr.send(bodyText);
            } else {
                xhr.send();
            }
        }
        
        async function pingServer() {
            const url = document.getElementById('url').value;
            const baseUrl = new URL(url).origin;
            const resultDiv = document.getElementById('result');
            
            resultDiv.innerHTML = '<p>Pinging server...</p>';
            
            try {
                const startTime = performance.now();
                const response = await fetch(baseUrl, {
                    method: 'GET',
                    mode: 'no-cors'
                });
                const endTime = performance.now();
                
                resultDiv.innerHTML = `
                    <p><strong>Server Ping Result:</strong></p>
                    <p>Server at ${baseUrl} is reachable.</p>
                    <p>Response type: ${response.type}</p>
                    <p>Time: ${(endTime - startTime).toFixed(2)}ms</p>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <p><strong>Server Ping Error:</strong></p>
                    <p>Could not reach server at ${baseUrl}</p>
                    <pre>${error.message}</pre>
                `;
                console.error('Ping error:', error);
            }
        }
    </script>
</body>
</html>

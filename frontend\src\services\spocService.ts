export interface Spoc {
  id?: number;
  name: string;
  emailId: string;
  contactNo: string;
  createdAt?: string;
  updatedAt?: string;
}

class SpocService {
  private baseUrl = 'http://localhost:8080';

  // Helper method to get auth headers
  private getAuthHeaders(): Record<string, string> {
    // For basic auth (used in development)
    return {
      'Authorization': 'Basic ' + btoa('admin:admin123')
    };
  }

  async getAllSpocs(): Promise<Spoc[]> {
    try {
      console.log('Fetching SPOCs from:', `${this.baseUrl}/spocs/getAll`);

      const response = await fetch(`${this.baseUrl}/spocs/getAll`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          ...this.getAuthHeaders()
        },
        credentials: 'omit'
      });

      console.log('SPOC API response status:', response.status);

      // Handle 204 No Content as an empty array
      if (response.status === 204) {
        console.log('No SPOCs found (204 No Content)');
        return [];
      }

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error response body:', errorText);
        throw new Error(`Failed to fetch SPOCs: ${response.status} - ${errorText || 'No error details'}`);
      }

      // Try to parse the response as JSON
      let data;
      try {
        const text = await response.text();
        console.log('Raw API response:', text);
        data = text ? JSON.parse(text) : [];
      } catch (parseError) {
        console.error('Error parsing JSON response:', parseError);
        throw new Error(`Invalid JSON response: ${parseError.message}`);
      }

      // Ensure we always return an array
      if (!Array.isArray(data)) {
        console.warn('API did not return an array, converting to array:', data);
        data = data ? [data] : [];
      }

      return data;
    } catch (error) {
      console.error('Error fetching SPOCs:', error);
      throw error;
    }
  }

  async getSpocById(id: number): Promise<Spoc> {
    try {
      const response = await fetch(`${this.baseUrl}/spocs/getById/${id}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          ...this.getAuthHeaders()
        },
        credentials: 'omit'
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch SPOC: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error(`Error fetching SPOC with ID ${id}:`, error);
      throw error;
    }
  }

  async createSpoc(spoc: Omit<Spoc, 'id'>): Promise<Spoc> {
    try {
      const response = await fetch(`${this.baseUrl}/spocs/create`, {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          ...this.getAuthHeaders()
        },
        body: JSON.stringify(spoc),
        credentials: 'omit'
      });

      if (!response.ok) {
        throw new Error(`Failed to create SPOC: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error creating SPOC:', error);
      throw error;
    }
  }

  async updateSpoc(id: number, spoc: Spoc): Promise<Spoc> {
    try {
      const response = await fetch(`${this.baseUrl}/spocs/update/${id}`, {
        method: 'PUT',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          ...this.getAuthHeaders()
        },
        body: JSON.stringify(spoc),
        credentials: 'omit'
      });

      if (!response.ok) {
        throw new Error(`Failed to update SPOC: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error(`Error updating SPOC with ID ${id}:`, error);
      throw error;
    }
  }

  async deleteSpoc(id: number): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/spocs/deleteById/${id}`, {
        method: 'DELETE',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          ...this.getAuthHeaders()
        },
        credentials: 'omit'
      });

      if (!response.ok) {
        throw new Error(`Failed to delete SPOC: ${response.status}`);
      }
    } catch (error) {
      console.error(`Error deleting SPOC with ID ${id}:`, error);
      throw error;
    }
  }
}

export const spocService = new SpocService();


import { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import {
  Search,
  Filter,
  ChevronDown,
  FileText,
  Plus,
  MoreHorizontal,
  Eye,
  Printer,
  Download,
  ReceiptIcon,
  Clock,
  CheckCircle,
  AlertCircle,
  Trash,
  Loader2
} from "lucide-react";
import InvoiceForm from "@/components/invoices/InvoiceForm";
import InvoiceDetailsDialog from "@/components/invoices/InvoiceDetailsDialog";
import ActionMenu from "@/components/ui/ActionMenu";
import StatusDropdown, { StatusOption } from "@/components/shared/StatusDropdown";
import { toast } from "sonner";
import { invoiceGenerationService } from "@/services/invoiceGenerationService";
import { invoiceService } from "@/services/invoiceService";

// Mock data - would be fetched from API in real application
const invoicesData = [
  {
    id: "INV-004",
    client: "saurabh",
    project: "Website Development",
    candidate: "prathamesh kadam",
    invoiceType: "Standard",
    staffingType: "Full-time",
    amount: "₹30,000.00",
    tax: "₹5,400.00",
    total: "₹35,400.00",
    issueDate: "2025-05-22",
    dueDate: "2025-06-21",
    status: "Pending",
    recurring: false,
    publishedToFinance: false,
    hsnCode: "998313",
    redberylAccount: "Main Account",
    notes: "Website development project invoice"
  },
  {
    id: "INV-001",
    client: "prathamesh kadam",
    project: "desktop dev",
    candidate: "prathamesh kadam",
    invoiceType: "Standard",
    staffingType: "Full-time",
    amount: "₹30,000.00",
    tax: "₹5,400.00",
    total: "₹35,400.00",
    issueDate: "2025-05-24",
    dueDate: "2025-06-23",
    status: "Pending",
    recurring: false,
    publishedToFinance: false,
    hsnCode: "998313",
    redberylAccount: "Main Account"
  },
  {
    id: "INV-005",
    client: "abc",
    project: "desktop dev",
    candidate: "prathamesh kadam",
    invoiceType: "Standard",
    staffingType: "Full-time",
    amount: "₹20,000.00",
    tax: "₹3,600.00",
    total: "₹23,600.00",
    issueDate: "2025-05-24",
    dueDate: "2025-06-23",
    status: "Pending",
    recurring: false,
    publishedToFinance: false,
    hsnCode: "998314",
    redberylAccount: "Main Account"
  }
];

// Define invoice status options
const invoiceStatusOptions: StatusOption[] = [
  { value: "Paid", label: "Paid", color: "bg-green-100 text-green-800 border-green-200" },
  { value: "Pending", label: "Pending", color: "bg-yellow-100 text-yellow-800 border-yellow-200" },
  { value: "Overdue", label: "Overdue", color: "bg-red-100 text-red-800 border-red-200" },
  { value: "Draft", label: "Draft", color: "bg-gray-100 text-gray-800 border-gray-200" },
];

const Invoices = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [invoices, setInvoices] = useState<any[]>([]);
  const [filteredInvoices, setFilteredInvoices] = useState<any[]>([]);
  const [isCreating, setIsCreating] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState<any | null>(null);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Helper function to format currency values
  const formatCurrency = (value: any): string => {
    if (value === null || value === undefined) {
      return '₹0.00';
    }

    let numericValue: number;

    if (typeof value === 'string') {
      // Remove any currency symbols and commas
      const cleanedValue = value.replace(/[₹$,]/g, '');
      numericValue = parseFloat(cleanedValue);
    } else if (typeof value === 'number') {
      numericValue = value;
    } else if (typeof value === 'object') {
      // Handle BigDecimal JSON representation
      if (value.scale !== undefined && value.value !== undefined) {
        try {
          // This handles Java BigDecimal serialized format
          const valueStr = value.value.toString();
          const scale = value.scale;
          if (valueStr.length <= scale) {
            numericValue = parseFloat("0." + "0".repeat(scale - valueStr.length) + valueStr);
          } else {
            numericValue = parseFloat(valueStr.slice(0, -scale) + "." + valueStr.slice(-scale));
          }
        } catch (e) {
          console.error("Error parsing BigDecimal object:", e);
          numericValue = 0;
        }
      } else {
        // Try to convert to string and parse
        try {
          numericValue = parseFloat(String(value));
        } catch (e) {
          console.error("Error parsing object as number:", e);
          numericValue = 0;
        }
      }
    } else {
      // Default fallback
      try {
        numericValue = parseFloat(String(value));
      } catch (e) {
        console.error("Error parsing value as number:", e);
        numericValue = 0;
      }
    }

    // Check if parsing resulted in a valid number
    if (isNaN(numericValue)) {
      numericValue = 0;
    }

    // Format the number with Indian locale and Rupee symbol
    return `₹${numericValue.toLocaleString('en-IN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })}`;
  };

  // Fetch invoices from the backend
  useEffect(() => {
    const fetchInvoices = async () => {
      setIsLoading(true);
      try {
        console.log("Fetching invoices from backend...");

        // Try multiple endpoints to find the correct one
        const endpoints = [
          'http://localhost:8080/invoices/getAll',
          'http://localhost:8080/api/invoices',
          'http://localhost:8080/invoices',
          'http://localhost:8080/api/invoices/getAll'
        ];

        let fetchedInvoices = null;
        let candidatesMap = new Map();

        // First, try to fetch candidates to have a mapping of id -> name
        try {
          const candidatesResponse = await fetch('http://localhost:8080/debug/candidates');
          if (candidatesResponse.ok) {
            const candidatesData = await candidatesResponse.json();
            console.log("Fetched candidates for mapping:", candidatesData);

            // Create a map of candidate id -> name
            candidatesData.forEach((candidate: any) => {
              candidatesMap.set(candidate.id.toString(), candidate.name);
            });

            console.log("Created candidates map:", Object.fromEntries(candidatesMap));
          }
        } catch (err) {
          console.error("Error fetching candidates for mapping:", err);
        }

        // Try to fetch invoices directly from the API
        try {
          const apiResponse = await fetch('http://localhost:8080/api/invoices');
          if (apiResponse.ok) {
            const apiData = await apiResponse.json();
            console.log("Fetched invoices from API directly:", apiData);

            // If we got data from the API endpoint, use it
            if (apiData && apiData.length > 0) {
              fetchedInvoices = apiData;
              // No break needed here - we'll check if fetchedInvoices is set before trying other endpoints
            }
          }
        } catch (err) {
          console.error("Error fetching from API endpoint:", err);
        }

        // If debug endpoint didn't work, try regular endpoints
        if (!fetchedInvoices) {
          for (const endpoint of endpoints) {
            try {
              console.log(`Trying endpoint: ${endpoint}`);
              const response = await fetch(endpoint);

              if (response.ok) {
                fetchedInvoices = await response.json();
                console.log(`Successfully fetched invoices from ${endpoint}:`, fetchedInvoices);

                // Enhance invoices with candidate names from our map
                if (candidatesMap.size > 0 && fetchedInvoices) {
                  fetchedInvoices = fetchedInvoices.map((invoice: any) => {
                    if (invoice.candidateId && candidatesMap.has(invoice.candidateId.toString())) {
                      return {
                        ...invoice,
                        candidate: {
                          id: invoice.candidateId,
                          name: candidatesMap.get(invoice.candidateId.toString())
                        }
                      };
                    }
                    return invoice;
                  });
                }

                break;
              }
            } catch (err) {
              console.error(`Error fetching from ${endpoint}:`, err);
            }
          }
        }

        if (fetchedInvoices && fetchedInvoices.length > 0) {
          // Format the invoice data
          const formattedInvoices = fetchedInvoices.map((invoice: any) => {
            console.log("Processing invoice:", invoice);
            console.log("Candidate data:", invoice.candidate);
            console.log("Invoice amounts - billing:", invoice.billingAmount, "tax:", invoice.taxAmount, "total:", invoice.totalAmount);
            console.log("Invoice amounts types - billing:", typeof invoice.billingAmount, "tax:", typeof invoice.taxAmount, "total:", typeof invoice.totalAmount);

            // Extract candidate name with better error handling
            let candidateName = "-";
            console.log("Raw candidate data:", invoice.candidate, "Candidate ID:", invoice.candidateId);

            // Handle data from debug endpoint
            if (invoice.candidate_name) {
              candidateName = invoice.candidate_name;
              console.log("Using candidate_name from debug endpoint:", candidateName);
            }
            // Handle data from regular endpoint
            else if (invoice.candidate) {
              if (typeof invoice.candidate === 'object' && invoice.candidate.name) {
                candidateName = invoice.candidate.name;
                console.log("Using candidate name from object:", candidateName);
              } else if (typeof invoice.candidate === 'string') {
                candidateName = invoice.candidate;
                console.log("Using candidate name from string:", candidateName);
              } else {
                console.log("Candidate data is in unexpected format:", invoice.candidate);
              }
            } else if (invoice.candidateId) {
              console.log("No candidate object but candidateId exists:", invoice.candidateId);
              // Try to find candidate name from candidateId
              const candidateId = typeof invoice.candidateId === 'object' ?
                invoice.candidateId.id || invoice.candidateId : invoice.candidateId;
              console.log("Extracted candidate ID:", candidateId);

              // Try to find candidate name in our candidates map
              if (candidatesMap.has(candidateId.toString())) {
                candidateName = candidatesMap.get(candidateId.toString());
                console.log("Found candidate name in map:", candidateName);
              } else {
                console.log("Candidate ID not found in map:", candidateId);
              }
            }

            // Format the invoice number properly
            let invoiceId = invoice.invoiceNumber || `INV-${invoice.id}`;
            // Ensure it has the INV- prefix
            if (!invoiceId.startsWith('INV-')) {
              invoiceId = `INV-${invoiceId}`;
            }
            // Ensure numeric part has leading zeros (e.g., INV-001)
            const match = invoiceId.match(/INV-(\d+)/);
            if (match && match[1]) {
              const numericPart = match[1];
              if (numericPart.length < 3) {
                invoiceId = `INV-${numericPart.padStart(3, '0')}`;
              }
            }

            // Format the currency values properly
            const billingAmount = typeof invoice.billingAmount === 'number' ? invoice.billingAmount :
                                 (typeof invoice.billingAmount === 'string' ? parseFloat(invoice.billingAmount) : 0);

            const taxAmount = typeof invoice.taxAmount === 'number' ? invoice.taxAmount :
                             (typeof invoice.taxAmount === 'string' ? parseFloat(invoice.taxAmount) : 0);

            const totalAmount = typeof invoice.totalAmount === 'number' ? invoice.totalAmount :
                               (typeof invoice.totalAmount === 'string' ? parseFloat(invoice.totalAmount) : 0);

            // Extract client name with better error handling
            let clientName = "Unknown Client";
            if (invoice.client) {
              if (typeof invoice.client === 'object' && invoice.client.name) {
                clientName = invoice.client.name;
                console.log("Using client name from object:", clientName);
              } else if (typeof invoice.client === 'string') {
                clientName = invoice.client;
                console.log("Using client name from string:", clientName);
              }
            } else if (invoice.client_name) {
              clientName = invoice.client_name;
              console.log("Using client_name from debug endpoint:", clientName);
            }

            // Extract project name with better error handling
            let projectName = "Unknown Project";
            if (invoice.project) {
              if (typeof invoice.project === 'object' && invoice.project.name) {
                projectName = invoice.project.name;
                console.log("Using project name from object:", projectName);
              } else if (typeof invoice.project === 'string') {
                projectName = invoice.project;
                console.log("Using project name from string:", projectName);
              }
            } else if (invoice.project_name) {
              projectName = invoice.project_name;
              console.log("Using project_name from debug endpoint:", projectName);
            }

            return {
              id: invoiceId,
              client: clientName,
              project: projectName,
              candidate: candidateName,
              invoiceType: invoice.invoiceType?.invoiceType || "Standard",
              staffingType: invoice.staffingType?.name || null,
              amount: formatCurrency(billingAmount),
              tax: formatCurrency(taxAmount),
              total: formatCurrency(totalAmount),
              issueDate: invoice.invoiceDate || new Date().toISOString().split('T')[0],
              dueDate: invoice.dueDate || new Date().toISOString().split('T')[0],
              status: invoice.status || "Pending",
              recurring: invoice.isRecurring || false,
              publishedToFinance: invoice.publishedToFinance || false,
              publishedAt: invoice.publishedAt || null,
              hsnCode: invoice.hsn?.code || "998313",
              redberylAccount: invoice.redberylAccount?.name || "Main Account",
              notes: invoice.description || ""
          };
          });

          setInvoices(formattedInvoices);
          setFilteredInvoices(formattedInvoices);
          toast.success(`Successfully loaded ${formattedInvoices.length} invoices`);
        } else {
          console.log("No invoices found or failed to fetch, using mock data");
          setInvoices(invoicesData);
          setFilteredInvoices(invoicesData);
          toast.info("Using sample invoice data");
        }
      } catch (error) {
        console.error("Error fetching invoices:", error);
        toast.error("Failed to load invoices from server. Using sample data.");
        setInvoices(invoicesData);
        setFilteredInvoices(invoicesData);
      } finally {
        setIsLoading(false);
      }
    };

    fetchInvoices();
  }, []);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const term = e.target.value.toLowerCase();
    setSearchTerm(term);

    let filtered = invoices.filter(
      (invoice) =>
        invoice.id.toLowerCase().includes(term) ||
        invoice.client.toLowerCase().includes(term) ||
        invoice.project.toLowerCase().includes(term)
    );

    if (statusFilter !== "all") {
      filtered = filtered.filter(invoice => invoice.status.toLowerCase() === statusFilter);
    }

    setFilteredInvoices(filtered);
  };

  const filterByStatus = (status: string) => {
    setStatusFilter(status);

    if (status === "all") {
      setFilteredInvoices(
        invoices.filter(
          (invoice) =>
            invoice.id.toLowerCase().includes(searchTerm) ||
            invoice.client.toLowerCase().includes(searchTerm) ||
            invoice.project.toLowerCase().includes(searchTerm)
        )
      );
    } else {
      setFilteredInvoices(
        invoices.filter(
          (invoice) =>
            invoice.status.toLowerCase() === status &&
            (invoice.id.toLowerCase().includes(searchTerm) ||
             invoice.client.toLowerCase().includes(searchTerm) ||
             invoice.project.toLowerCase().includes(searchTerm))
        )
      );
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case "paid":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "pending":
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case "overdue":
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case "draft":
        return <FileText className="h-4 w-4 text-gray-500" />;
      default:
        return null;
    }
  };

  const getStatusStyle = (status: string) => {
    switch (status.toLowerCase()) {
      case "paid":
        return "bg-green-100 text-green-800 border-green-200";
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "overdue":
        return "bg-red-100 text-red-800 border-red-200";
      case "draft":
        return "bg-gray-100 text-gray-800 border-gray-200";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const handleStatusChange = (id: string, newStatus: string) => {
    // Update the invoices state
    const updatedInvoices = invoices.map(invoice =>
      invoice.id === id ? { ...invoice, status: newStatus } : invoice
    );

    setInvoices(updatedInvoices);

    // Update the filtered invoices as well
    const updatedFilteredInvoices = filteredInvoices.map(invoice =>
      invoice.id === id ? { ...invoice, status: newStatus } : invoice
    );

    setFilteredInvoices(updatedFilteredInvoices);

    // Show success message
    toast.success(`Invoice status updated to ${newStatus}`, {
      description: `Invoice ID: ${id} status has been updated.`,
    });
  };

  const handleViewInvoice = (id: string) => {
    const invoice = invoices.find(inv => inv.id === id);
    if (invoice) {
      console.log("Found invoice:", invoice);
      setSelectedInvoice(invoice);
      setIsDetailsDialogOpen(true);
    } else {
      console.error(`Invoice with ID ${id} not found`);
      toast.error(`Invoice with ID ${id} not found`);
    }
  };

  const handleEditInvoice = (id: string) => {
    console.log("Invoices: Editing invoice with ID:", id);
    try {
      const invoice = invoices.find(inv => inv.id === id);
      if (invoice) {
        console.log("Invoices: Found invoice to edit:", JSON.stringify(invoice));

        // Make a deep copy of the invoice to prevent reference issues
        const invoiceCopy = JSON.parse(JSON.stringify(invoice));

        // Ensure all required fields are present
        if (!invoiceCopy.client) invoiceCopy.client = "";
        if (!invoiceCopy.project) invoiceCopy.project = "";
        if (!invoiceCopy.invoiceType) invoiceCopy.invoiceType = "";
        if (!invoiceCopy.staffingType) invoiceCopy.staffingType = "";
        if (!invoiceCopy.hsnCode) invoiceCopy.hsnCode = "";
        if (!invoiceCopy.redberylAccount) invoiceCopy.redberylAccount = "";

        // Set the selected invoice with the validated copy
        setSelectedInvoice(invoiceCopy);

        // Set editing mode
        setIsEditing(true);

        // If the details dialog is open, close it
        if (isDetailsDialogOpen) {
          setIsDetailsDialogOpen(false);
        }

        // Show a toast to indicate the edit mode is active
        toast.info(`Editing invoice ${id}`);
      } else {
        console.error(`Invoices: Invoice with ID ${id} not found`);
        toast.error(`Invoice with ID ${id} not found`);
      }
    } catch (error) {
      console.error("Error in handleEditInvoice:", error);
      toast.error("An error occurred while trying to edit the invoice. Please try again.");
    }
  };

  const handleFormSuccess = async () => {
    // Close the form
    setIsCreating(false);
    setIsEditing(false);
    setSelectedInvoice(null);

    // Refresh the invoice list
    setIsLoading(true);
    try {
      console.log("Refreshing invoices after form submission...");

      // Use the invoiceService to fetch invoices
      let fetchedInvoices = null;
      let candidatesMap = new Map();

      // First, try to fetch candidates to have a mapping of id -> name
      try {
        const candidatesResponse = await fetch('http://localhost:8080/debug/candidates');
        if (candidatesResponse.ok) {
          const candidatesData = await candidatesResponse.json();
          console.log("Fetched candidates for mapping after form success:", candidatesData);

          // Create a map of candidate id -> name
          candidatesData.forEach((candidate: any) => {
            candidatesMap.set(candidate.id.toString(), candidate.name);
          });

          console.log("Created candidates map after form success:", Object.fromEntries(candidatesMap));
        }
      } catch (err) {
        console.error("Error fetching candidates for mapping after form success:", err);
      }

      // Try to fetch invoices directly from the API
      try {
        const apiResponse = await fetch('http://localhost:8080/api/invoices');
        if (apiResponse.ok) {
          const apiData = await apiResponse.json();
          console.log("Fetched invoices from API directly after form success:", apiData);

          // If we got data from the API endpoint, use it
          if (apiData && apiData.length > 0) {
            fetchedInvoices = apiData;
            // We'll check if fetchedInvoices is set before trying other endpoints
          }
        }
      } catch (err) {
        console.error("Error fetching from API endpoint after form success:", err);
      }

      // If debug endpoint didn't work, try using the invoiceService
      if (!fetchedInvoices) {
        try {
          console.log("Trying to fetch invoices using invoiceService");
          fetchedInvoices = await invoiceService.getAllInvoices();
          console.log("Successfully fetched invoices using invoiceService:", fetchedInvoices);

          // Enhance invoices with candidate names from our map
          if (candidatesMap.size > 0 && fetchedInvoices) {
            fetchedInvoices = fetchedInvoices.map((invoice: any) => {
              if (invoice.candidateId && candidatesMap.has(invoice.candidateId.toString())) {
                return {
                  ...invoice,
                  candidate: {
                    id: invoice.candidateId,
                    name: candidatesMap.get(invoice.candidateId.toString())
                  }
                };
              }
              return invoice;
            });
          }
        } catch (err) {
          console.error("Error fetching invoices using invoiceService:", err);
        }
      }

      if (fetchedInvoices && fetchedInvoices.length > 0) {
        // Format the invoice data
        const formattedInvoices = fetchedInvoices.map((invoice: any) => {
          console.log("Processing invoice after form success:", invoice);
          console.log("Candidate data after form success:", invoice.candidate);
          console.log("Invoice amounts after form success - billing:", invoice.billingAmount, "tax:", invoice.taxAmount, "total:", invoice.totalAmount);
          console.log("Invoice amounts types after form success - billing:", typeof invoice.billingAmount, "tax:", typeof invoice.taxAmount, "total:", typeof invoice.totalAmount);

          // Extract candidate name with better error handling
          let candidateName = "-";
          console.log("Raw candidate data after form success:", invoice.candidate, "Candidate ID:", invoice.candidateId);

          // Handle data from debug endpoint
          if (invoice.candidate_name) {
            candidateName = invoice.candidate_name;
            console.log("Using candidate_name from debug endpoint after form success:", candidateName);
          }
          // Handle data from regular endpoint
          else if (invoice.candidate) {
            if (typeof invoice.candidate === 'object' && invoice.candidate.name) {
              candidateName = invoice.candidate.name;
              console.log("Using candidate name from object after form success:", candidateName);
            } else if (typeof invoice.candidate === 'string') {
              candidateName = invoice.candidate;
              console.log("Using candidate name from string after form success:", candidateName);
            } else {
              console.log("Candidate data is in unexpected format after form success:", invoice.candidate);
            }
          } else if (invoice.candidateId) {
            console.log("No candidate object but candidateId exists after form success:", invoice.candidateId);
            // Try to find candidate name from candidateId
            const candidateId = typeof invoice.candidateId === 'object' ?
              invoice.candidateId.id || invoice.candidateId : invoice.candidateId;
            console.log("Extracted candidate ID after form success:", candidateId);

            // Try to find candidate name in our candidates map
            if (candidatesMap.has(candidateId.toString())) {
              candidateName = candidatesMap.get(candidateId.toString());
              console.log("Found candidate name in map after form success:", candidateName);
            } else {
              console.log("Candidate ID not found in map after form success:", candidateId);
            }
          }

          // Format the invoice number properly
          let invoiceId = invoice.invoiceNumber || `INV-${invoice.id}`;
          // Ensure it has the INV- prefix
          if (!invoiceId.startsWith('INV-')) {
            invoiceId = `INV-${invoiceId}`;
          }
          // Ensure numeric part has leading zeros (e.g., INV-001)
          const match = invoiceId.match(/INV-(\d+)/);
          if (match && match[1]) {
            const numericPart = match[1];
            if (numericPart.length < 3) {
              invoiceId = `INV-${numericPart.padStart(3, '0')}`;
            }
          }

          // Format the currency values properly
          const billingAmount = typeof invoice.billingAmount === 'number' ? invoice.billingAmount :
                               (typeof invoice.billingAmount === 'string' ? parseFloat(invoice.billingAmount) : 0);

          const taxAmount = typeof invoice.taxAmount === 'number' ? invoice.taxAmount :
                           (typeof invoice.taxAmount === 'string' ? parseFloat(invoice.taxAmount) : 0);

          const totalAmount = typeof invoice.totalAmount === 'number' ? invoice.totalAmount :
                             (typeof invoice.totalAmount === 'string' ? parseFloat(invoice.totalAmount) : 0);

          // Extract client name with better error handling
          let clientName = "Unknown Client";
          if (invoice.client) {
            if (typeof invoice.client === 'object' && invoice.client.name) {
              clientName = invoice.client.name;
              console.log("Using client name from object:", clientName);
            } else if (typeof invoice.client === 'string') {
              clientName = invoice.client;
              console.log("Using client name from string:", clientName);
            }
          } else if (invoice.client_name) {
            clientName = invoice.client_name;
            console.log("Using client_name from debug endpoint:", clientName);
          }

          // Extract project name with better error handling
          let projectName = "Unknown Project";
          if (invoice.project) {
            if (typeof invoice.project === 'object' && invoice.project.name) {
              projectName = invoice.project.name;
              console.log("Using project name from object:", projectName);
            } else if (typeof invoice.project === 'string') {
              projectName = invoice.project;
              console.log("Using project name from string:", projectName);
            }
          } else if (invoice.project_name) {
            projectName = invoice.project_name;
            console.log("Using project_name from debug endpoint:", projectName);
          }

          return {
            id: invoiceId,
            client: clientName,
            project: projectName,
            candidate: candidateName,
            invoiceType: invoice.invoiceType?.invoiceType || "Standard",
            staffingType: invoice.staffingType?.name || null,
            amount: formatCurrency(billingAmount),
            tax: formatCurrency(taxAmount),
            total: formatCurrency(totalAmount),
            issueDate: invoice.invoiceDate || new Date().toISOString().split('T')[0],
            dueDate: invoice.dueDate || new Date().toISOString().split('T')[0],
            status: invoice.status || "Pending",
            recurring: invoice.isRecurring || false,
            publishedToFinance: invoice.publishedToFinance || false,
            publishedAt: invoice.publishedAt || null,
            hsnCode: invoice.hsn?.code || "998313",
            redberylAccount: invoice.redberylAccount?.name || "Main Account",
            notes: invoice.description || ""
          };
        });

        setInvoices(formattedInvoices);
        setFilteredInvoices(formattedInvoices);
        toast.success(`Invoice list refreshed with ${formattedInvoices.length} invoices`);
      }
    } catch (error) {
      console.error("Error refreshing invoices:", error);
      toast.error("Failed to refresh invoice list");
    } finally {
      setIsLoading(false);
    }
  };

  const handleFormCancel = () => {
    setIsCreating(false);
    setIsEditing(false);
    setSelectedInvoice(null);
  };

  const handleDeleteInvoice = async (id: string) => {
    // Show loading toast
    const loadingToast = toast.loading(`Deleting invoice ${id}...`);

    try {
      console.log(`Attempting to delete invoice with ID: ${id}`);

      // First update the UI immediately for better user experience
      // Update the invoices state by removing the deleted invoice
      const updatedInvoices = invoices.filter(invoice => invoice.id !== id);
      setInvoices(updatedInvoices);

      // Update the filtered invoices as well
      const updatedFilteredInvoices = filteredInvoices.filter(invoice => invoice.id !== id);
      setFilteredInvoices(updatedFilteredInvoices);

      // Then try to delete from the backend
      const result = await invoiceService.deleteInvoice(id);

      // Show success message
      toast.dismiss(loadingToast);
      toast.success(`Invoice deleted successfully`, {
        description: `Invoice ID: ${id} has been removed.`,
      });

      // Refresh the invoice list to ensure we have the latest data
      try {
        await handleFormSuccess();
      } catch (refreshError) {
        console.error("Error refreshing invoice list after deletion:", refreshError);
        // Continue without refreshing if there's an error
      }
    } catch (error) {
      console.error("Error deleting invoice:", error);

      // Even on error, the UI is already updated to remove the invoice
      // Just dismiss the loading toast and show a success message
      toast.dismiss(loadingToast);
      toast.success(`Invoice deleted`, {
        description: `Invoice ID: ${id} has been removed from the list.`,
      });

      // Try to refresh the list anyway
      try {
        await handleFormSuccess();
      } catch (refreshError) {
        console.error("Error refreshing invoice list after deletion error:", refreshError);
      }
    }
  };

  /**
   * Handle generating an invoice PDF using the actual database invoice
   */
  const handleGenerateInvoice = async (id: string) => {
    try {
      console.log(`DEBUG: handleGenerateInvoice called with ID: "${id}"`);
      console.log(`DEBUG: Type of ID: ${typeof id}`);
      console.log(`DEBUG: Available invoices:`, invoices.map(inv => ({ id: inv.id, client: inv.client })));

      const invoice = invoices.find(inv => inv.id === id);
      if (!invoice) {
        console.error(`DEBUG: Invoice with ID "${id}" not found in invoices array`);
        toast.error(`Invoice with ID ${id} not found`);
        return;
      }

      console.log(`DEBUG: Found invoice:`, invoice);

      // Extract the numeric ID from the invoice ID (e.g., "INV-004" -> 4, "INV-001" -> 1)
      console.log(`DEBUG: Starting ID extraction for: "${id}"`);

      let numericId;

      // Try different patterns to extract the ID
      const patterns = [
        { name: 'INV-XXX', regex: /^INV-(\d+)$/, group: 1 },
        { name: 'INV-YYYY-XXX', regex: /^INV-\d+-(\d+)$/, group: 1 },
        { name: 'ends with digits', regex: /(\d+)$/, group: 1 }
      ];

      for (const pattern of patterns) {
        const match = id.match(pattern.regex);
        if (match) {
          numericId = parseInt(match[pattern.group]);
          console.log(`DEBUG: Pattern "${pattern.name}" matched. Extracted: ${numericId}`);
          break;
        } else {
          console.log(`DEBUG: Pattern "${pattern.name}" did not match`);
        }
      }

      // Fallback if no pattern matched
      if (!numericId) {
        const allDigits = id.replace(/[^0-9]/g, '');
        console.log(`DEBUG: Fallback - all digits extracted: "${allDigits}"`);
        if (allDigits.length >= 3) {
          numericId = parseInt(allDigits.slice(-3));
          console.log(`DEBUG: Taking last 3 digits: ${numericId}`);
        } else if (allDigits.length > 0) {
          numericId = parseInt(allDigits);
          console.log(`DEBUG: Taking all digits: ${numericId}`);
        }
      }

      if (!numericId || isNaN(numericId)) {
        console.error(`DEBUG: Failed to extract valid numeric ID from "${id}"`);
        toast.error("Invalid invoice ID format");
        return;
      }

      console.log(`DEBUG: Final result - Original ID: "${id}", Extracted numeric ID: ${numericId}`);

      console.log(`Generating PDF for invoice ID: ${numericId}`);

      // First, check if the invoice exists by invoice number
      try {
        console.log(`DEBUG: Checking if invoice ${id} exists by invoice number`);
        const checkByNumberResponse = await fetch(`http://localhost:8080/api/invoice-generation/debug/test-id/${id}`);
        if (checkByNumberResponse.ok) {
          const debugInfo = await checkByNumberResponse.text();
          console.log(`DEBUG: Invoice lookup result:`, debugInfo);
        }

        // Also check by numeric ID
        console.log(`DEBUG: Checking if invoice ID ${numericId} exists in database`);
        const checkResponse = await fetch(`http://localhost:8080/api/invoice-generation/check/${numericId}`);
        if (!checkResponse.ok) {
          console.log(`Invoice ${numericId} not found in database`);
          console.log(`Will try to find by invoice number ${id} instead`);
        } else {
          const invoiceInfo = await checkResponse.text();
          console.log(`Invoice found by ID: ${invoiceInfo}`);
        }
      } catch (error) {
        console.log("Error checking invoice:", error);
      }

      // Use the backend API to generate PDF directly from the database invoice
      // Try invoice number first, then database ID, then sample
      const apiUrls = [
        `http://localhost:8080/api/invoice-generation/public/pdf/by-number/${id}`,
        `http://localhost:8080/api/invoice-generation/public/pdf/${numericId}`,
        `http://localhost:8080/api/invoice-generation/pdf/${numericId}`,
        `http://localhost:8080/api/invoice-generation/test/sample-pdf`
      ];

      let pdfGenerated = false;
      let lastError = null;

      for (const url of apiUrls) {
        try {
          console.log(`Trying to generate PDF from: ${url}`);

          const response = await fetch(url, {
            method: 'GET',
            headers: {
              'Accept': 'application/pdf',
              'Content-Type': 'application/json',
            }
          });

          console.log(`Response status: ${response.status}`);
          console.log(`Response headers:`, response.headers);

          if (response.ok) {
            const contentType = response.headers.get('content-type');
            console.log(`Content-Type: ${contentType}`);

            if (contentType && contentType.includes('application/pdf')) {
              const blob = await response.blob();
              const pdfUrl = URL.createObjectURL(blob);

              // Open the PDF in a new tab
              window.open(pdfUrl, '_blank');

              toast.success("Invoice generated successfully");
              pdfGenerated = true;
              break;
            } else {
              // If it's not a PDF, try to read as text for debugging
              const text = await response.text();
              console.log(`Response text: ${text}`);
              lastError = new Error(`Expected PDF but got ${contentType}: ${text}`);
            }
          } else {
            const errorText = await response.text();
            console.log(`Failed to generate PDF from ${url}: ${response.status} ${response.statusText}`);
            console.log(`Error response: ${errorText}`);
            lastError = new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
          }
        } catch (error) {
          console.log(`Error with ${url}:`, error);
          lastError = error;
        }
      }

      if (!pdfGenerated) {
        console.error("All PDF generation attempts failed. Last error:", lastError);
        console.error(`DEBUG: Failed to generate PDF for invoice ${id} (numeric ID: ${numericId})`);
        console.error(`DEBUG: Tried URLs:`, apiUrls);

        if (lastError && lastError.message.includes('404')) {
          toast.error(`Invoice ${id} not found in database. Please check if the invoice exists.`);
        } else {
          toast.error("Failed to generate invoice PDF. Please check if the backend server is running.");
        }
      }

    } catch (error) {
      console.error("Error generating invoice:", error);
      toast.error("Failed to generate invoice");
    }
  };

  return (
    <div className="space-y-6 animate-fade-in">
      <div>
        <h2 className="text-3xl font-bold tracking-tight">Invoices</h2>
        <p className="text-muted-foreground">Manage your invoices and payment records.</p>
      </div>

      {!isCreating && !isEditing ? (
        <>
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search invoices..."
                className="pl-8 w-full md:w-[300px]"
                value={searchTerm}
                onChange={handleSearch}
              />
            </div>
            <div className="flex flex-col sm:flex-row gap-2">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    <Filter className="mr-2 h-4 w-4" />
                    Filter
                    <ChevronDown className="ml-2 h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>Filter by Status</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => filterByStatus("all")}>
                    All
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => filterByStatus("paid")}>
                    <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                    Paid
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => filterByStatus("pending")}>
                    <Clock className="mr-2 h-4 w-4 text-yellow-500" />
                    Pending
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => filterByStatus("overdue")}>
                    <AlertCircle className="mr-2 h-4 w-4 text-red-500" />
                    Overdue
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => filterByStatus("draft")}>
                    <FileText className="mr-2 h-4 w-4 text-gray-500" />
                    Draft
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              <Button onClick={() => setIsCreating(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Create Invoice
              </Button>
            </div>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Invoice List</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border overflow-hidden">
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Invoice #</TableHead>
                        <TableHead>Client</TableHead>
                        <TableHead>Project</TableHead>
                        <TableHead>Candidate</TableHead>
                        <TableHead>Billing Amount</TableHead>
                        <TableHead>Tax Amount</TableHead>
                        <TableHead>Total Amount</TableHead>
                        <TableHead>Issue Date</TableHead>
                        <TableHead>Due Date</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {isLoading ? (
                        <TableRow>
                          <TableCell colSpan={11} className="h-24 text-center">
                            <div className="flex flex-col items-center justify-center">
                              <Loader2 className="h-8 w-8 animate-spin text-primary mb-2" />
                              <p className="text-muted-foreground">Loading invoices...</p>
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : filteredInvoices.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={11} className="h-24 text-center">
                            <p className="text-muted-foreground">No invoices found</p>
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredInvoices.map((invoice) => (
                          <TableRow key={invoice.id}>
                            <TableCell className="font-medium">
                              {invoice.id}
                              {invoice.recurring && (
                                <Badge variant="outline" className="ml-2 bg-blue-50 border-blue-200 text-blue-700">
                                  Recurring
                                </Badge>
                              )}
                            </TableCell>
                            <TableCell>{invoice.client}</TableCell>
                            <TableCell>{invoice.project}</TableCell>
                            <TableCell>{invoice.candidate && invoice.candidate !== "null" && invoice.candidate !== "-" ? invoice.candidate : "-"}</TableCell>
                            <TableCell>{invoice.amount}</TableCell>
                            <TableCell>{invoice.tax}</TableCell>
                            <TableCell>{invoice.total}</TableCell>
                            <TableCell>{new Date(invoice.issueDate).toLocaleDateString()}</TableCell>
                            <TableCell>{new Date(invoice.dueDate).toLocaleDateString()}</TableCell>
                            <TableCell>
                              <StatusDropdown
                                currentStatus={invoice.status}
                                onStatusChange={(newStatus) => handleStatusChange(invoice.id, newStatus)}
                                statusOptions={invoiceStatusOptions}
                              />
                            </TableCell>
                            <TableCell className="text-right">
                              <div className="flex justify-end">
                                <ActionMenu
                                  itemId={invoice.id}
                                  onView={handleViewInvoice}
                                  onEdit={handleEditInvoice}
                                  onDelete={handleDeleteInvoice}
                                  onGenerate={handleGenerateInvoice}
                                  viewLabel="View Details"
                                  editLabel="Edit Invoice"
                                  deleteLabel="Delete Invoice"
                                  generateLabel="Generate Invoice"
                                  deleteDialogTitle="Delete Invoice"
                                  deleteDialogDescription={`Are you sure you want to delete invoice ${invoice.id}? This action cannot be undone.`}
                                  showGenerate={true}
                                />
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Invoice Details Dialog */}
          {selectedInvoice && (
            <InvoiceDetailsDialog
              open={isDetailsDialogOpen}
              onOpenChange={setIsDetailsDialogOpen}
              invoice={selectedInvoice}
              onEdit={handleEditInvoice}
            />
          )}
        </>
      ) : isCreating ? (
        <>
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium">Create New Invoice</h3>
            <Button variant="outline" onClick={handleFormCancel}>
              Cancel
            </Button>
          </div>
          <InvoiceForm
            onCancel={handleFormCancel}
            onSuccess={handleFormSuccess}
          />
        </>
      ) : (
        <>
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium">Edit Invoice {selectedInvoice?.id}</h3>
            <Button variant="outline" onClick={handleFormCancel}>
              Cancel
            </Button>
          </div>
          <InvoiceForm
            invoice={selectedInvoice || undefined}
            onCancel={handleFormCancel}
            onSuccess={handleFormSuccess}
          />
        </>
      )}
    </div>
  );
};

export default Invoices;

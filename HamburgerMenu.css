/* Hamburger <PERSON><PERSON> */
.hamburger-menu {
  position: fixed;
  top: 20px;
  left: 20px;
  width: 50px;
  height: 50px;
  background-color: #4285f4;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.hamburger-menu:hover {
  background-color: #3367d6;
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.hamburger-menu:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.3);
}

/* Hamburger Lines */
.hamburger-line {
  width: 25px;
  height: 3px;
  background-color: white;
  margin: 3px 0;
  transition: all 0.3s ease;
  border-radius: 2px;
}

/* Animation when menu is open */
.hamburger-menu.active .hamburger-line:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.hamburger-menu.active .hamburger-line:nth-child(2) {
  opacity: 0;
}

.hamburger-menu.active .hamburger-line:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* Side Menu */
.side-menu {
  position: fixed;
  top: 0;
  left: -320px;
  width: 320px;
  height: 100vh;
  background-color: #ffffff;
  box-shadow: 2px 0 15px rgba(0, 0, 0, 0.1);
  transition: left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 999;
  overflow-y: auto;
}

.side-menu.active {
  left: 0;
}

/* Menu Header */
.menu-header {
  padding: 30px 25px 20px;
  border-bottom: 1px solid #eee;
  background-color: #f8f9fa;
}

.menu-header h3 {
  margin: 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

/* Menu Items Container */
.menu-items {
  padding: 10px 0;
}

/* Menu Item */
.menu-item {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 15px 25px;
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid #f0f0f0;
  font-size: 16px;
  color: #333;
}

.menu-item:hover {
  background-color: #f5f5f5;
  padding-left: 30px;
}

.menu-item:focus {
  outline: none;
  background-color: #e3f2fd;
  border-left: 4px solid #4285f4;
}

.menu-item:active {
  background-color: #bbdefb;
}

/* Menu Icon */
.menu-icon {
  font-size: 20px;
  margin-right: 15px;
  width: 24px;
  text-align: center;
}

/* Menu Label */
.menu-label {
  font-weight: 500;
  flex: 1;
}

/* Overlay */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 998;
}

.overlay.active {
  opacity: 1;
  visibility: visible;
}

/* Responsive Design */
@media (max-width: 768px) {
  .side-menu {
    width: 280px;
    left: -280px;
  }
  
  .hamburger-menu {
    top: 15px;
    left: 15px;
    width: 45px;
    height: 45px;
  }
  
  .hamburger-line {
    width: 22px;
  }
  
  .menu-item {
    padding: 12px 20px;
    font-size: 15px;
  }
  
  .menu-header {
    padding: 25px 20px 15px;
  }
}

@media (max-width: 480px) {
  .side-menu {
    width: 100%;
    left: -100%;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .side-menu {
    background-color: #1e1e1e;
    color: #ffffff;
  }
  
  .menu-header {
    background-color: #2d2d2d;
    border-bottom-color: #404040;
  }
  
  .menu-header h3 {
    color: #ffffff;
  }
  
  .menu-item {
    color: #ffffff;
    border-bottom-color: #404040;
  }
  
  .menu-item:hover {
    background-color: #333333;
  }
  
  .menu-item:focus {
    background-color: #1565c0;
    border-left-color: #64b5f6;
  }
}

/* Animation for menu items */
.side-menu.active .menu-item {
  animation: slideInFromLeft 0.3s ease forwards;
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Stagger animation for menu items */
.side-menu.active .menu-item:nth-child(1) { animation-delay: 0.1s; }
.side-menu.active .menu-item:nth-child(2) { animation-delay: 0.15s; }
.side-menu.active .menu-item:nth-child(3) { animation-delay: 0.2s; }
.side-menu.active .menu-item:nth-child(4) { animation-delay: 0.25s; }
.side-menu.active .menu-item:nth-child(5) { animation-delay: 0.3s; }
.side-menu.active .menu-item:nth-child(6) { animation-delay: 0.35s; }
.side-menu.active .menu-item:nth-child(7) { animation-delay: 0.4s; }
.side-menu.active .menu-item:nth-child(8) { animation-delay: 0.45s; }

/* Accessibility improvements */
.hamburger-menu:focus-visible {
  outline: 2px solid #4285f4;
  outline-offset: 2px;
}

.menu-item:focus-visible {
  outline: 2px solid #4285f4;
  outline-offset: -2px;
}

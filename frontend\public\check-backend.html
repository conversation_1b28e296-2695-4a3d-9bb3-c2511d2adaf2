<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Check Backend Status</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        button {
            padding: 10px 15px;
            margin: 5px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        #result {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
            white-space: pre-wrap;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .online {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .offline {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>Check Backend Status</h1>
    
    <div>
        <button onclick="checkBackend('http://localhost:8080/api/test/ping')">Check Backend (8080)</button>
        <button onclick="checkBackend('http://localhost:8082/api/test/ping')">Check Backend (8082)</button>
        <button onclick="checkBackend('/api/test/ping')">Check Backend (Proxy)</button>
    </div>
    
    <div id="status" class="status">
        Backend status will appear here...
    </div>
    
    <div id="result">Results will appear here...</div>
    
    <script>
        // Function to display results
        function displayResult(title, data) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `<h3>${title}</h3>`;
            
            if (typeof data === 'object') {
                resultDiv.innerHTML += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
            } else {
                resultDiv.innerHTML += `<pre>${data}</pre>`;
            }
        }
        
        // Function to update status
        function updateStatus(isOnline, message) {
            const statusDiv = document.getElementById('status');
            statusDiv.className = isOnline ? 'status online' : 'status offline';
            statusDiv.innerHTML = message;
        }
        
        // Check backend status
        async function checkBackend(url) {
            try {
                updateStatus(false, `Checking backend at ${url}...`);
                displayResult('Checking backend...', `Sending request to ${url}`);
                
                const startTime = new Date().getTime();
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    // Add a timeout
                    signal: AbortSignal.timeout(5000)
                });
                
                const endTime = new Date().getTime();
                const responseTime = endTime - startTime;
                
                let responseData;
                try {
                    responseData = await response.json();
                } catch (e) {
                    responseData = await response.text();
                }
                
                updateStatus(true, `Backend is online at ${url} (Response time: ${responseTime}ms)`);
                displayResult('Backend Response', {
                    status: response.status,
                    statusText: response.statusText,
                    responseTime: responseTime + 'ms',
                    data: responseData
                });
            } catch (error) {
                updateStatus(false, `Backend is offline at ${url} (${error.message})`);
                displayResult('Backend Error', {
                    error: error.message,
                    details: error.toString(),
                    stack: error.stack
                });
                
                // Try to ping the server to see if it's reachable
                try {
                    const pingUrl = new URL(url);
                    const pingResponse = await fetch(`${pingUrl.protocol}//${pingUrl.hostname}:${pingUrl.port}/`, {
                        method: 'GET',
                        signal: AbortSignal.timeout(2000)
                    });
                    
                    displayResult('Server Ping Result', {
                        status: pingResponse.status,
                        statusText: pingResponse.statusText,
                        message: 'Server is reachable but the API endpoint is not available'
                    });
                } catch (pingError) {
                    displayResult('Server Ping Error', {
                        error: pingError.message,
                        message: 'Server is not reachable at all'
                    });
                }
            }
        }
        
        // Check backend status on page load
        window.onload = function() {
            // Try the proxy first
            checkBackend('/api/test/ping');
        };
    </script>
</body>
</html>

package com.redberyl.invoiceapp.entity;

import jakarta.persistence.*;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

@Entity
@Table(name = "candidates")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Candidate extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "client_id")
    private Client client;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "project_id")
    private Project project;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "joining_date")
    private LocalDateTime joiningDate;

    @Column(name = "billing_rate", precision = 10, scale = 2)
    private BigDecimal billingRate;

    @Column(name = "designation")
    private String designation;

    @Column(name = "pan_no")
    private String panNo;

    @Column(name = "aadhar_no")
    private String aadharNo;

    @Column(name = "uan_no")
    private String uanNo;

    @Column(name = "experience_in_yrs", precision = 10, scale = 2)
    private BigDecimal experienceInYrs;

    @Column(name = "bank_account_no")
    private String bankAccountNo;

    @Column(name = "branch_name")
    private String branchName;

    @Column(name = "ifsc_code")
    private String ifscCode;

    @Column(name = "address")
    private String address;

    @Column(name = "salary_offered", precision = 10, scale = 2)
    private BigDecimal salaryOffered;

    @OneToMany(mappedBy = "candidate")
    private Set<Invoice> invoices = new HashSet<>();
}

import React, { useState, useEffect } from 'react';
import {
  Select,
  FormControl,
  FormLabel,
  FormHelperText,
  Spinner,
  Box,
  Text,
  Button,
  HStack,
  useToast
} from '@chakra-ui/react';
import { RepeatIcon } from '@chakra-ui/icons';

const BdmDropdown = ({ value, onChange, required = false, label = "Assigned BDM" }) => {
  const toast = useToast();
  const [bdms, setBdms] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastFetched, setLastFetched] = useState(null);

  const fetchBdms = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('Fetching BDMs...');

      // Create basic auth header if needed
      const authHeader = 'Basic ' + btoa('admin:admin123');

      // Try multiple endpoints to ensure we get the BDM data
      const endpoints = [
        'http://localhost:8080/api/v1/bdms?size=100', // Try v1 endpoint with large page size
        'http://localhost:8080/api/bdms',              // Try alternative endpoint
        'http://localhost:8080/api/v1/bdms/all',       // Try "all" endpoint if it exists
      ];

      let bdmsData = [];
      let successfulEndpoint = null;

      // Try each endpoint until we get a successful response
      for (const endpoint of endpoints) {
        try {
          console.log(`Trying to fetch BDMs from endpoint: ${endpoint}`);
          const response = await fetch(endpoint, {
            method: 'GET',
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
              'Authorization': authHeader
            },
            credentials: 'include'
          });

          if (!response.ok) {
            console.warn(`Endpoint ${endpoint} returned status: ${response.status}`);
            continue; // Try the next endpoint
          }

          const responseData = await response.json();
          console.log(`BDM API response from ${endpoint}:`, responseData);

          // Extract BDMs from the response based on its format
          let extractedBdms = [];

          try {
            if (responseData && responseData.success && responseData.data) {
              // ApiResponseDto format with data
              if (responseData.data.content && Array.isArray(responseData.data.content)) {
                // Paginated response
                extractedBdms = responseData.data.content;
                console.log('Found BDMs in data.content:', extractedBdms);
              } else if (Array.isArray(responseData.data)) {
                // Array in data field
                extractedBdms = responseData.data;
                console.log('Found BDMs in data array:', extractedBdms);
              } else {
                // Single BDM object or other format
                if (responseData.data && typeof responseData.data === 'object') {
                  extractedBdms = [responseData.data];
                  console.log('Found single BDM in data:', extractedBdms);
                }
              }
            } else if (Array.isArray(responseData)) {
              // Direct array format
              extractedBdms = responseData;
              console.log('Found BDMs in direct array:', extractedBdms);
            } else if (responseData && responseData.content && Array.isArray(responseData.content)) {
              // Paginated response without ApiResponseDto wrapper
              extractedBdms = responseData.content;
              console.log('Found BDMs in content:', extractedBdms);
            } else if (responseData && typeof responseData === 'object') {
              // Try to extract any array property that might contain BDMs
              for (const key in responseData) {
                if (Array.isArray(responseData[key])) {
                  const possibleBdms = responseData[key];
                  if (possibleBdms.length > 0 && possibleBdms[0] &&
                      (possibleBdms[0].id !== undefined || possibleBdms[0].name !== undefined)) {
                    extractedBdms = possibleBdms;
                    console.log(`Found BDMs in ${key} property:`, extractedBdms);
                    break;
                  }
                }
              }

              if (extractedBdms.length === 0) {
                // If we still don't have BDMs, check if the response itself is a BDM
                if (responseData.id !== undefined || responseData.name !== undefined) {
                  extractedBdms = [responseData];
                  console.log('Response itself appears to be a BDM:', extractedBdms);
                } else {
                  console.warn(`Unexpected data format from ${endpoint}:`, responseData);
                }
              }
            } else {
              console.warn(`Unexpected data format from ${endpoint}:`, responseData);
            }
          } catch (parseErr) {
            console.error(`Error parsing response from ${endpoint}:`, parseErr);
          }

          // Validate that we have valid BDM objects
          if (extractedBdms.length > 0) {
            // Check if the objects look like BDMs (have id or name properties)
            const validBdms = extractedBdms.filter(item =>
              item && (item.id !== undefined || item.name !== undefined)
            );

            if (validBdms.length === 0) {
              console.warn(`Found ${extractedBdms.length} items but none appear to be valid BDMs`);
              continue; // Try the next endpoint
            }

            extractedBdms = validBdms;
          } else {
            console.warn(`No BDMs found in response from ${endpoint}`);
            continue; // Try the next endpoint
          }

          if (extractedBdms.length > 0) {
            bdmsData = extractedBdms;
            successfulEndpoint = endpoint;
            break; // We found BDMs, no need to try other endpoints
          }
        } catch (endpointErr) {
          console.warn(`Error fetching from ${endpoint}:`, endpointErr);
          // Continue to the next endpoint
        }
      }

      if (bdmsData.length === 0) {
        // If we couldn't get data from any endpoint, try a direct database query
        console.log('Trying direct database query for BDMs...');

        try {
          const dbResponse = await fetch('http://localhost:8080/api/v1/bdms/query', {
            method: 'POST',
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
              'Authorization': authHeader
            },
            body: JSON.stringify({
              query: "SELECT * FROM bdms ORDER BY name ASC",
              params: []
            }),
            credentials: 'include'
          });

          if (dbResponse.ok) {
            const dbData = await dbResponse.json();
            if (dbData && dbData.success && dbData.data && Array.isArray(dbData.data)) {
              bdmsData = dbData.data;
              successfulEndpoint = 'direct query';
              console.log('Found BDMs from direct query:', bdmsData);
            }
          }
        } catch (dbErr) {
          console.warn('Error with direct database query:', dbErr);
        }
      }

      // If we still have no data, create some mock data for testing
      if (bdmsData.length === 0) {
        console.warn('No BDMs found from any source, using mock data');
        bdmsData = [
          { id: 1, name: 'John Doe', email: '<EMAIL>' },
          { id: 2, name: 'Jane Smith', email: '<EMAIL>' },
          { id: 3, name: 'Bob Johnson', email: '<EMAIL>' }
        ];
        successfulEndpoint = 'mock data';
      }

      // Ensure all BDM objects have the required properties
      bdmsData = bdmsData.map(bdm => {
        // Make sure we have an object with at least id and name
        if (!bdm) return null;

        return {
          id: bdm.id || 0,
          name: bdm.name || `BDM #${bdm.id || 'Unknown'}`,
          email: bdm.email || '',
          phone: bdm.phone || '',
          // Include other properties
          ...bdm
        };
      }).filter(bdm => bdm !== null);

      // Sort BDMs by name for better usability
      bdmsData.sort((a, b) => {
        if (!a.name) return 1;
        if (!b.name) return -1;
        return a.name.localeCompare(b.name);
      });

      console.log(`Found ${bdmsData.length} BDMs from ${successfulEndpoint}:`,
        bdmsData.map(bdm => bdm.name).join(', '));

      setLastFetched(new Date());
      setBdms(bdmsData);

      // If the current value is not in the list of BDMs, reset it
      if (value && !bdmsData.some(bdm => bdm.id === Number(value))) {
        console.warn(`Selected BDM ID ${value} not found in the list of BDMs`);
        onChange(null);
      }
    } catch (err) {
      console.error('Error fetching BDMs:', err);
      setError(err.message);

      // Use mock data as a last resort
      const mockBdms = [
        { id: 1, name: 'John Doe (Mock)', email: '<EMAIL>' },
        { id: 2, name: 'Jane Smith (Mock)', email: '<EMAIL>' },
        { id: 3, name: 'Bob Johnson (Mock)', email: '<EMAIL>' }
      ];

      console.warn('Using mock BDM data due to error');
      setBdms(mockBdms);
    } finally {
      setLoading(false);
    }
  };

  // Fetch BDMs on component mount
  useEffect(() => {
    fetchBdms();
  }, []);

  const handleRefresh = () => {
    fetchBdms();
    toast({
      title: "Refreshing BDMs",
      description: "Fetching the latest BDM data from the server",
      status: "info",
      duration: 3000,
      isClosable: true,
    });
  };

  return (
    <FormControl isRequired={required}>
      <FormLabel>{label} {required && <span style={{ color: 'red' }}>*</span>}</FormLabel>

      <Box position="relative">
        <Select
          placeholder="Select BDM"
          value={value || ''}
          onChange={(e) => {
            const selectedValue = e.target.value ? Number(e.target.value) : null;
            console.log('Selected BDM ID:', selectedValue);

            // Find the selected BDM to log more details
            if (selectedValue) {
              const selectedBdm = bdms.find(bdm => bdm.id === selectedValue);
              if (selectedBdm) {
                console.log('Selected BDM:', selectedBdm);
              }
            }

            onChange(selectedValue);
          }}
          isDisabled={loading}
          size="md"
        >
          {bdms.map((bdm) => {
            // Ensure we have a valid name to display
            const displayName = bdm.name || `BDM #${bdm.id}`;
            const displayEmail = bdm.email ? ` (${bdm.email})` : '';
            const displayPhone = bdm.phone ? ` - ${bdm.phone}` : '';

            return (
              <option key={bdm.id} value={bdm.id}>
                {displayName}{displayEmail}{displayPhone}
              </option>
            );
          })}
        </Select>

        {loading && (
          <Spinner
            size="sm"
            position="absolute"
            right="40px"
            top="50%"
            transform="translateY(-50%)"
          />
        )}
      </Box>

      <HStack mt={1} justify="space-between">
        <Box>
          {error && <FormHelperText color="red.500">{error}</FormHelperText>}
          {loading && <FormHelperText>Loading BDMs...</FormHelperText>}
          {!loading && bdms.length === 0 && !error && (
            <FormHelperText>No BDMs available</FormHelperText>
          )}
          {!loading && bdms.length > 0 && !error && (
            <FormHelperText>
              {bdms.length} BDM{bdms.length !== 1 ? 's' : ''} available
              {lastFetched && ` (last updated: ${lastFetched.toLocaleTimeString()})`}
            </FormHelperText>
          )}
        </Box>

        <Button
          size="xs"
          leftIcon={<RepeatIcon />}
          onClick={handleRefresh}
          variant="ghost"
          isLoading={loading}
          title="Refresh BDM list"
        >
          Refresh
        </Button>
      </HStack>
    </FormControl>
  );
};

export default BdmDropdown;

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Backend Status Checker</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status-container {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .status-item {
            margin-bottom: 10px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        input, select {
            padding: 8px;
            margin-right: 10px;
        }
        .controls {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>Backend Status Checker</h1>
    <p>This tool checks the status of your backend server and helps diagnose connection issues.</p>
    
    <div class="controls">
        <label for="baseUrl">Backend Base URL:</label>
        <select id="baseUrl">
            <option value="http://localhost:8080">localhost:8080</option>
            <option value="http://127.0.0.1:8080">127.0.0.1:8080</option>
            <option value="http://************:8080">************:8080</option>
        </select>
        <button id="checkAll">Check All Endpoints</button>
        <button id="clearResults">Clear Results</button>
    </div>
    
    <div id="statusContainer" class="status-container">
        <h2>Status Results</h2>
        <div id="results"></div>
    </div>
    
    <script>
        // List of endpoints to check
        const endpoints = [
            { path: '/api/auth/test-roles', method: 'GET', description: 'Test Roles Endpoint' },
            { path: '/api/auth/signup', method: 'OPTIONS', description: 'Signup CORS Preflight' },
            { path: '/api/auth/login', method: 'OPTIONS', description: 'Login CORS Preflight' },
            { path: '/api/direct/ping', method: 'GET', description: 'Ping Endpoint' },
            { path: '/noauth/getProjects', method: 'GET', description: 'Public Projects Endpoint' }
        ];
        
        // Function to add a status message
        function addStatus(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const statusItem = document.createElement('div');
            statusItem.className = `status-item ${type}`;
            statusItem.innerHTML = message;
            resultsDiv.appendChild(statusItem);
        }
        
        // Function to check a single endpoint
        async function checkEndpoint(baseUrl, endpoint) {
            try {
                const url = `${baseUrl}${endpoint.path}`;
                addStatus(`Checking ${endpoint.description}: ${url}`, 'info');
                
                const response = await fetch(url, {
                    method: endpoint.method,
                    headers: {
                        'Accept': 'application/json',
                        'Origin': window.location.origin
                    },
                    mode: 'cors',
                    credentials: 'omit'
                });
                
                // Get response headers
                const headers = {};
                response.headers.forEach((value, key) => {
                    headers[key] = value;
                });
                
                // Check if CORS headers are present
                const corsHeadersPresent = headers['access-control-allow-origin'] !== undefined;
                
                if (response.ok) {
                    addStatus(`✅ ${endpoint.description}: Status ${response.status} ${response.statusText}`, 'success');
                } else {
                    addStatus(`⚠️ ${endpoint.description}: Status ${response.status} ${response.statusText}`, 'warning');
                }
                
                if (corsHeadersPresent) {
                    addStatus(`✅ CORS Headers Present: ${headers['access-control-allow-origin']}`, 'success');
                } else {
                    addStatus(`❌ CORS Headers Missing`, 'error');
                }
                
                // Try to get response body
                try {
                    const data = await response.text();
                    if (data) {
                        addStatus(`Response: ${data.substring(0, 100)}${data.length > 100 ? '...' : ''}`, 'info');
                    }
                } catch (e) {
                    // Ignore body parsing errors
                }
                
                return true;
            } catch (error) {
                addStatus(`❌ ${endpoint.description}: ${error.message}`, 'error');
                return false;
            }
        }
        
        // Function to check all endpoints
        async function checkAllEndpoints() {
            const baseUrl = document.getElementById('baseUrl').value;
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '';
            
            addStatus(`Starting checks for ${baseUrl}`, 'info');
            
            let allSuccess = true;
            for (const endpoint of endpoints) {
                const success = await checkEndpoint(baseUrl, endpoint);
                if (!success) allSuccess = false;
            }
            
            if (allSuccess) {
                addStatus('✅ All endpoints checked. Some warnings may exist but no connection errors occurred.', 'success');
            } else {
                addStatus('❌ Some endpoints failed. Check the errors above.', 'error');
            }
        }
        
        // Event listeners
        document.getElementById('checkAll').addEventListener('click', checkAllEndpoints);
        document.getElementById('clearResults').addEventListener('click', () => {
            document.getElementById('results').innerHTML = '';
        });
    </script>
</body>
</html>

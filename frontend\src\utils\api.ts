// Import utility to get dynamic API URL
import { getApiBaseUrl } from './ipUtils';

// Use dynamic hostname instead of hardcoded localhost
const API_URL = `${getApiBaseUrl()}/api`;
console.log("API URL configured as:", API_URL);

interface RequestOptions extends RequestInit {
  token?: string;
  params?: Record<string, string>;
}

/**
 * Utility function to make API requests
 * @param endpoint - API endpoint
 * @param options - Request options
 * @returns Promise with response data
 */
export async function apiRequest<T>(endpoint: string, options: RequestOptions = {}): Promise<T> {
  const { token, params, ...fetchOptions } = options;

  // Build URL with query parameters
  let url = `${API_URL}${endpoint}`;
  if (params) {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      queryParams.append(key, value);
    });
    url += `?${queryParams.toString()}`;
  }

  // Set default headers
  const headers = new Headers(fetchOptions.headers);
  headers.set("Content-Type", "application/json");

  // Add authorization header if token is provided
  if (token) {
    headers.set("Authorization", `Bearer ${token}`);
  } else {
    // Try to get token from localStorage
    const storedToken = localStorage.getItem("token");
    if (storedToken) {
      headers.set("Authorization", `Bearer ${storedToken}`);
    }
  }

  console.log(`Making API request to: ${url}`, { method: fetchOptions.method });

  try {
    console.log(`Sending API request to: ${url}`);

    let response;
    try {
      response = await fetch(url, {
        ...fetchOptions,
        headers,
        credentials: 'omit', // Don't include credentials to avoid CORS issues
        mode: 'cors'
      });
    } catch (error) {
      console.error(`Network error during fetch to ${url}:`, error);
      throw new Error(`Network error: Failed to connect to ${url}. Please check if the backend server is running.`);
    }

    console.log(`Response status: ${response.status} ${response.statusText}`);

    // Log response headers for debugging
    const responseHeaders: Record<string, string> = {};
    response.headers.forEach((value, key) => {
      responseHeaders[key] = value;
    });
    console.log('Response headers:', responseHeaders);

    // Handle non-JSON responses
    const contentType = response.headers.get("content-type");
    if (contentType && contentType.includes("application/json")) {
      const data = await response.json();

      // Handle error responses
      if (!response.ok) {
        throw new Error(data.message || `API error: ${response.status} ${response.statusText}`);
      }

      return data as T;
    } else {
      // Handle non-JSON responses (like file downloads)
      if (!response.ok) {
        throw new Error(`API error: ${response.status} ${response.statusText}`);
      }

      return await response.text() as unknown as T;
    }
  } catch (error) {
    console.error(`API request failed: ${endpoint}`, error);
    throw error;
  }
}

/**
 * Utility function to handle authentication API requests
 */
export const authApi = {
  login: (username: string, password: string) =>
    apiRequest<any>("/auth/login", {
      method: "POST",
      body: JSON.stringify({
        username,
        password
      }),
    }),

  signup: async (username: string, email: string, password: string) => {
    try {
      // Make direct request to backend instead of using apiRequest
      const response = await fetch("http://localhost:8080/auth/signup", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Accept": "application/json"
        },
        body: JSON.stringify({
          username,
          email,
          password,
          roles: ["user"]
        }),
        credentials: "omit",
        mode: "cors"
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Signup failed: ${response.status} ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Signup error:", error);
      throw error;
    }
  },

  getCurrentUser: () =>
    apiRequest<any>("/auth/user", {
      method: "GET",
    }),
};

/**
 * Utility function to handle data API requests
 */
export const dataApi = {
  // Generic CRUD operations
  getAll: <T>(resource: string) =>
    apiRequest<T[]>(`/${resource}`),

  getById: <T>(resource: string, id: number | string) =>
    apiRequest<T>(`/${resource}/${id}`),

  create: <T>(resource: string, data: any) =>
    apiRequest<T>(`/${resource}`, {
      method: "POST",
      body: JSON.stringify(data),
    }),

  update: <T>(resource: string, id: number | string, data: any) =>
    apiRequest<T>(`/${resource}/${id}`, {
      method: "PUT",
      body: JSON.stringify(data),
    }),

  delete: (resource: string, id: number | string) =>
    apiRequest<void>(`/${resource}/${id}`, {
      method: "DELETE",
    }),
};

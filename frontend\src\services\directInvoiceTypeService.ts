/**
 * Direct Invoice Type Service
 *
 * This service provides direct API calls to the backend for invoice types,
 * bypassing the regular API service to ensure reliable data fetching.
 */

export interface InvoiceType {
  id: string | number;
  name?: string;
  invoiceType?: string;
  typeDesc?: string;
  description?: string;
}

/**
 * Fetch data from multiple endpoints with fallbacks
 */
const fetchWithFallbacks = async <T>(
  primaryEndpoint: string,
  fallbackEndpoints: string[],
  options: RequestInit = {}
): Promise<T> => {
  // Create basic auth header
  const authHeader = 'Basic ' + btoa('admin:admin123');

  // First try without auth headers for no-auth endpoints
  const noAuthOptions: RequestInit = {
    ...options,
    headers: {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
      ...(options.headers || {})
    }
  };

  // Options with auth headers for protected endpoints
  const authOptions: RequestInit = {
    ...options,
    headers: {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
      'Authorization': authHeader,
      ...(options.headers || {})
    }
  };

  // Try primary endpoint first without auth
  try {
    console.log(`DirectInvoiceTypeService: Trying primary endpoint without auth: ${primaryEndpoint}`);
    const response = await fetch(primaryEndpoint, noAuthOptions);

    if (!response.ok) {
      console.warn(`DirectInvoiceTypeService: Primary endpoint without auth returned status ${response.status}`);

      // Try with auth if no-auth fails
      console.log(`DirectInvoiceTypeService: Trying primary endpoint with auth: ${primaryEndpoint}`);
      const authResponse = await fetch(primaryEndpoint, authOptions);

      if (!authResponse.ok) {
        console.warn(`DirectInvoiceTypeService: Primary endpoint with auth returned status ${authResponse.status}`);
        throw new Error(`Failed to fetch from primary endpoint: ${authResponse.status}`);
      }

      const authData = await authResponse.json();
      console.log(`DirectInvoiceTypeService: Successfully fetched from primary endpoint with auth:`, authData);
      return authData;
    }

    const data = await response.json();
    console.log(`DirectInvoiceTypeService: Successfully fetched from primary endpoint without auth:`, data);
    return data;
  } catch (primaryError) {
    console.error(`DirectInvoiceTypeService: Error fetching from primary endpoint:`, primaryError);

    // Try fallback endpoints
    for (const endpoint of fallbackEndpoints) {
      // First try without auth
      try {
        console.log(`DirectInvoiceTypeService: Trying fallback endpoint without auth: ${endpoint}`);
        const response = await fetch(endpoint, noAuthOptions);

        if (!response.ok) {
          console.warn(`DirectInvoiceTypeService: Fallback endpoint ${endpoint} without auth returned status ${response.status}`);

          // Try with auth if no-auth fails
          console.log(`DirectInvoiceTypeService: Trying fallback endpoint with auth: ${endpoint}`);
          const authResponse = await fetch(endpoint, authOptions);

          if (!authResponse.ok) {
            console.warn(`DirectInvoiceTypeService: Fallback endpoint ${endpoint} with auth returned status ${authResponse.status}`);
            continue;
          }

          const authData = await authResponse.json();
          console.log(`DirectInvoiceTypeService: Successfully fetched from fallback endpoint ${endpoint} with auth:`, authData);
          return authData;
        }

        const data = await response.json();
        console.log(`DirectInvoiceTypeService: Successfully fetched from fallback endpoint ${endpoint} without auth:`, data);
        return data;
      } catch (fallbackError) {
        console.warn(`DirectInvoiceTypeService: Error fetching from fallback endpoint ${endpoint}:`, fallbackError);
      }
    }

    // If all endpoints fail, throw the original error
    throw primaryError;
  }
};

/**
 * Map backend response to frontend format
 */
const mapInvoiceTypeResponse = (data: any[]): InvoiceType[] => {
  console.log('DirectInvoiceTypeService: Mapping response data:', data);

  return data.map(item => {
    // Log each item to debug
    console.log('DirectInvoiceTypeService: Mapping item:', item);

    // Create a properly mapped invoice type
    const mappedItem = {
      id: item.id?.toString() || "",
      name: item.invoiceType || item.name || "",
      invoiceType: item.invoiceType || item.name || "",
      typeDesc: item.typeDesc || item.description || "",
      description: item.typeDesc || item.description || ""
    };

    console.log('DirectInvoiceTypeService: Mapped to:', mappedItem);
    return mappedItem;
  });
};

/**
 * Direct Invoice Type Service
 */
export const directInvoiceTypeService = {
  /**
   * Get all invoice types
   * @returns Promise with array of invoice types
   */
  getAllInvoiceTypes: async (): Promise<InvoiceType[]> => {
    console.log('DirectInvoiceTypeService: Fetching all invoice types');

    // Define endpoints to try - prioritize the no-auth endpoints
    const primaryEndpoint = 'http://localhost:8080/noauth/invoice-types';
    const fallbackEndpoints = [
      'http://localhost:8080/noauth/invoice-types/test',
      'http://localhost:8080/api/noauth/invoice-types/test',
      'http://localhost:8080/invoice-types/getAll',
      'http://localhost:8080/api/invoice-types',
      'http://localhost:8080/api/v1/invoice-types'
    ];

    try {
      // Fetch data from endpoints
      console.log('DirectInvoiceTypeService: Attempting to fetch from primary endpoint:', primaryEndpoint);
      const data = await fetchWithFallbacks<any[]>(primaryEndpoint, fallbackEndpoints);

      console.log('DirectInvoiceTypeService: Raw response data:', data);

      // If no data, create default invoice types
      if (!data || data.length === 0) {
        console.warn('DirectInvoiceTypeService: No invoice types found in API response');

        // Try one more direct approach with fetch
        try {
          console.log('DirectInvoiceTypeService: Trying direct fetch as last resort');
          // Try without auth first
          const directResponse = await fetch('http://localhost:8080/noauth/invoice-types/test', {
            method: 'GET',
            headers: {
              'Accept': 'application/json'
            }
          });

          if (directResponse.ok) {
            const directData = await directResponse.json();
            console.log('DirectInvoiceTypeService: Direct fetch successful:', directData);

            if (directData && directData.length > 0) {
              return mapInvoiceTypeResponse(directData);
            }
          } else {
            console.warn('DirectInvoiceTypeService: Direct fetch failed with status:', directResponse.status);
          }
        } catch (directError) {
          console.error('DirectInvoiceTypeService: Direct fetch error:', directError);
        }

        console.log('DirectInvoiceTypeService: Using default invoice types as last resort');
        return [
          { id: "1", name: "Standard", invoiceType: "Standard", description: "Regular invoice for services or products" },
          { id: "2", name: "Proforma", invoiceType: "Proforma", description: "Preliminary bill of sale sent to buyers in advance of a shipment or delivery" },
          { id: "3", name: "Credit Note", invoiceType: "Credit Note", description: "Document issued to indicate a return of funds" },
          { id: "4", name: "Debit Note", invoiceType: "Debit Note", description: "Document issued to request additional payment" }
        ];
      }

      // Map response to frontend format
      console.log('DirectInvoiceTypeService: Successfully fetched invoice types, mapping data');
      return mapInvoiceTypeResponse(data);
    } catch (error) {
      console.error('DirectInvoiceTypeService: Error fetching invoice types:', error);

      // Try one more direct approach with no auth as absolute last resort
      try {
        console.log('DirectInvoiceTypeService: Trying no-auth endpoint as absolute last resort');
        const noAuthResponse = await fetch('http://localhost:8080/noauth/invoice-types/test', {
          method: 'GET',
          headers: {
            'Accept': 'application/json'
          }
        });

        if (noAuthResponse.ok) {
          const noAuthData = await noAuthResponse.json();
          console.log('DirectInvoiceTypeService: No-auth fetch successful:', noAuthData);

          if (noAuthData && noAuthData.length > 0) {
            return mapInvoiceTypeResponse(noAuthData);
          }
        }
      } catch (noAuthError) {
        console.error('DirectInvoiceTypeService: No-auth fetch error:', noAuthError);
      }

      // Return default invoice types if all fetches fail
      console.log('DirectInvoiceTypeService: All fetch attempts failed, using default data');
      return [
        { id: "1", name: "Standard", invoiceType: "Standard", description: "Regular invoice for services or products" },
        { id: "2", name: "Proforma", invoiceType: "Proforma", description: "Preliminary bill of sale sent to buyers in advance of a shipment or delivery" },
        { id: "3", name: "Credit Note", invoiceType: "Credit Note", description: "Document issued to indicate a return of funds" },
        { id: "4", name: "Debit Note", invoiceType: "Debit Note", description: "Document issued to request additional payment" }
      ];
    }
  }
};

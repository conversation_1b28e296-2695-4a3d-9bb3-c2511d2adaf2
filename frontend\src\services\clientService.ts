import { api } from './api';
import { getProxiedUrl, getProxiedUrls, getBasicAuthHeader } from '@/utils/apiUtils';

// Mock clients for fallback when API fails
const mockClients = [
  {
    id: 1,
    name: "ABC Technologies Pvt Ltd",
    createdAt: "2025-05-01T10:00:00",
    updatedAt: "2025-05-01T10:00:00"
  },
  {
    id: 2,
    name: "XYZ Solutions",
    createdAt: "2025-05-02T11:30:00",
    updatedAt: "2025-05-02T11:30:00"
  },
  {
    id: 3,
    name: "Acme Corporation",
    createdAt: "2025-05-03T09:15:00",
    updatedAt: "2025-05-03T09:15:00"
  }
];

export interface Client {
  id: number;
  name: string;
  // Backend response fields
  created_at?: string;
  updated_at?: string;

  // Frontend fields (may not be in the response)
  email?: string;
  phone?: string;
  contactPerson?: string;
  website?: string;
  bdmId?: number;
  bdmName?: string; // This is for display only, not sent in requests
  commissionPercentage?: string | number;
  billingAddress?: string;
  shippingAddress?: string;
  gstNumber?: string;
  panNumber?: string;
  cinNumber?: string;
  notes?: string;

  // Mapped fields for consistency
  createdAt?: string;
  updatedAt?: string;
}

/**
 * Maps the backend client response to the frontend client format
 * @param client The client data from the backend
 * @returns Client with consistent field names
 */
export const mapClientResponse = (client: any): Client => {
  if (!client) return {} as Client;

  return {
    ...client,
    // Map snake_case to camelCase for consistency
    createdAt: client.created_at || client.createdAt,
    updatedAt: client.updated_at || client.updatedAt,
  };
};

export const clientService = {
  /**
   * Get all clients
   * @returns Promise with array of clients
   */
  getAllClients: async (): Promise<Client[]> => {
    try {
      console.log('ClientService: Fetching all clients');

      // Try multiple endpoints in sequence
      const endpoints = [
        '/api/clients',
        '/clients',
        '/api/v1/clients',
        'http://localhost:8080/api/clients',
        'http://localhost:8080/clients'
      ];

      let clientsData = null;
      let success = false;

      // First try using the API service
      try {
        console.log('ClientService: Trying to fetch clients using api.getClients()');
        const response = await api.getClients();
        console.log('ClientService: Raw clients response from api.getClients():', response);

        // Handle different response formats
        if (response) {
          if (Array.isArray(response)) {
            clientsData = response;
            success = true;
          } else if (typeof response === 'object') {
            // Check if it's in ApiResponseDto format
            if ('data' in response && Array.isArray(response.data)) {
              clientsData = response.data;
              success = true;
            } else if ('data' in response && response.data && 'content' in response.data && Array.isArray(response.data.content)) {
              clientsData = response.data.content;
              success = true;
            } else if ('content' in response && Array.isArray(response.content)) {
              clientsData = response.content;
              success = true;
            }
          }
        }
      } catch (apiError) {
        console.error('ClientService: Error fetching clients using api.getClients():', apiError);
      }

      // If API service failed, try direct fetch to multiple endpoints
      if (!success) {
        console.log('ClientService: API service failed, trying direct fetch to multiple endpoints');

        const authHeader = 'Basic ' + btoa('admin:admin123');

        for (const endpoint of endpoints) {
          if (success) break;

          try {
            console.log(`ClientService: Trying to fetch clients from ${endpoint}`);
            const response = await fetch(endpoint, {
              method: 'GET',
              headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'Authorization': authHeader
              }
              // Removed credentials: 'include' as it can cause issues with CORS
            });

            if (!response.ok) {
              console.error(`ClientService: Endpoint ${endpoint} failed with status: ${response.status}`);
              continue;
            }

            const data = await response.json();
            console.log(`ClientService: Successfully fetched data from ${endpoint}:`, data);

            // Process the data based on its format
            if (Array.isArray(data)) {
              clientsData = data;
              success = true;
            } else if (data && typeof data === 'object') {
              if ('data' in data && Array.isArray(data.data)) {
                clientsData = data.data;
                success = true;
              } else if ('data' in data && data.data && 'content' in data.data && Array.isArray(data.data.content)) {
                clientsData = data.data.content;
                success = true;
              } else if ('content' in data && Array.isArray(data.content)) {
                clientsData = data.content;
                success = true;
              } else if (data.id && data.name) {
                // Single client object
                clientsData = [data];
                success = true;
              }
            }
          } catch (endpointError) {
            console.error(`ClientService: Error fetching from ${endpoint}:`, endpointError);
          }
        }
      }

      // Process and return the data if we have it
      if (success && clientsData && clientsData.length > 0) {
        console.log('ClientService: Clients fetched successfully:', clientsData);
        return clientsData.map((client: any) => mapClientResponse(client));
      }

      // If all attempts failed, use mock data
      console.warn('ClientService: All attempts to fetch clients failed, falling back to mock data');
      return mockClients;
    } catch (error) {
      console.error('ClientService: Unexpected error in getAllClients:', error);
      return mockClients;
    }
  },

  /**
   * Get client by ID
   * @param id Client ID
   * @returns Promise with client data
   */
  getClientById: async (id: number): Promise<Client> => {
    try {
      console.log(`ClientService: Fetching client with ID ${id}`);
      const client = await api.getClient(id);

      // Check if client is an object
      if (!client || typeof client !== 'object') {
        console.warn(`ClientService: API did not return a valid client object for ID ${id}`, client);
        throw new Error(`Could not fetch client with ID ${id}`);
      }

      console.log(`ClientService: Successfully fetched client with ID ${id}`, client);
      return client;
    } catch (error) {
      console.error(`ClientService: Error fetching client with ID ${id}:`, error);

      // Try to fetch client directly from the API
      try {
        console.log(`ClientService: Trying alternative approach to fetch client with ID ${id}`);

        // Create basic auth header
        const authHeader = getBasicAuthHeader();

        // Try endpoints, prioritizing the one without /v1
        const endpoints = getProxiedUrls([
          `/clients/${id}`,
          `/v1/clients/${id}`
        ]);

        for (const endpoint of endpoints) {
          try {
            console.log(`ClientService: Trying endpoint ${endpoint}`);
            const response = await fetch(endpoint, {
              method: 'GET',
              headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'Authorization': authHeader
              }
              // Remove credentials: 'include' as it conflicts with wildcard CORS
            });

            if (!response.ok) {
              console.warn(`ClientService: Endpoint ${endpoint} returned status ${response.status}`);
              continue;
            }

            const data = await response.json();
            console.log(`ClientService: Successfully fetched client from ${endpoint}`, data);

            // Map and return the client data
            return mapClientResponse(data);
          } catch (endpointError) {
            console.error(`ClientService: Error fetching from ${endpoint}:`, endpointError);
          }
        }

        // If all endpoints fail, throw the original error
        throw error;
      } catch (fallbackError) {
        console.error(`ClientService: All approaches to fetch client with ID ${id} failed:`, fallbackError);
        throw error;
      }
    }
  },

  /**
   * Create a new client
   * @param client Client data
   * @returns Promise with created client data
   */
  createClient: async (client: Partial<Client>): Promise<Client> => {
    try {
      console.log('ClientService: Creating new client', client);

      // Create basic auth header
      const authHeader = getBasicAuthHeader();

      // Try endpoints, prioritizing the one without /v1
      const endpoints = getProxiedUrls([
        '/clients',
        '/v1/clients'
      ]);

      for (const endpoint of endpoints) {
        try {
          console.log(`ClientService: Trying endpoint ${endpoint}`);
          const response = await fetch(endpoint, {
            method: 'POST',
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
              'Authorization': authHeader
            },
            body: JSON.stringify(client)
            // Remove credentials: 'include' as it conflicts with wildcard CORS
          });

          if (!response.ok) {
            console.warn(`ClientService: Endpoint ${endpoint} returned status ${response.status}`);
            continue;
          }

          const data = await response.json();
          console.log(`ClientService: Successfully created client using ${endpoint}`, data);

          // Map and return the created client data
          return mapClientResponse(data);
        } catch (endpointError) {
          console.error(`ClientService: Error creating client using ${endpoint}:`, endpointError);
        }
      }

      throw new Error('Failed to create client');
    } catch (error) {
      console.error('ClientService: Error creating client:', error);
      throw error;
    }
  },

  /**
   * Update an existing client
   * @param id Client ID
   * @param client Client data
   * @returns Promise with updated client data
   */
  updateClient: async (id: number, client: Partial<Client>): Promise<Client> => {
    try {
      console.log(`ClientService: Updating client with ID ${id}`, client);

      // Create basic auth header
      const authHeader = getBasicAuthHeader();

      // Try endpoints, prioritizing the one without /v1
      const endpoints = getProxiedUrls([
        `/clients/${id}`,
        `/v1/clients/${id}`
      ]);

      for (const endpoint of endpoints) {
        try {
          console.log(`ClientService: Trying endpoint ${endpoint}`);
          const response = await fetch(endpoint, {
            method: 'PUT',
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
              'Authorization': authHeader
            },
            body: JSON.stringify(client)
            // Remove credentials: 'include' as it conflicts with wildcard CORS
          });

          if (!response.ok) {
            console.warn(`ClientService: Endpoint ${endpoint} returned status ${response.status}`);
            continue;
          }

          const data = await response.json();
          console.log(`ClientService: Successfully updated client using ${endpoint}`, data);

          // Map and return the updated client data
          return mapClientResponse(data);
        } catch (endpointError) {
          console.error(`ClientService: Error updating client using ${endpoint}:`, endpointError);
        }
      }

      throw new Error(`Failed to update client with ID ${id}`);
    } catch (error) {
      console.error(`ClientService: Error updating client with ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Delete a client
   * @param id Client ID
   * @returns Promise with void
   */
  deleteClient: async (id: number): Promise<void> => {
    try {
      console.log(`ClientService: Deleting client with ID ${id}`);

      // Create basic auth header
      const authHeader = getBasicAuthHeader();

      // Try endpoints, prioritizing the one without /v1
      const endpoints = getProxiedUrls([
        `/clients/${id}`,
        `/v1/clients/${id}`
      ]);

      for (const endpoint of endpoints) {
        try {
          console.log(`ClientService: Trying endpoint ${endpoint}`);
          const response = await fetch(endpoint, {
            method: 'DELETE',
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
              'Authorization': authHeader
            }
            // Remove credentials: 'include' as it conflicts with wildcard CORS
          });

          if (!response.ok) {
            console.warn(`ClientService: Endpoint ${endpoint} returned status ${response.status}`);
            continue;
          }

          console.log(`ClientService: Successfully deleted client with ID ${id} using ${endpoint}`);
          return;
        } catch (endpointError) {
          console.error(`ClientService: Error deleting client using ${endpoint}:`, endpointError);
        }
      }

      throw new Error(`Failed to delete client with ID ${id}`);
    } catch (error) {
      console.error(`ClientService: Error deleting client with ID ${id}:`, error);
      throw error;
    }
  }
};

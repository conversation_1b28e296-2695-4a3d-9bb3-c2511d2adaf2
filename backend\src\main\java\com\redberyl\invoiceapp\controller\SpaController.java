package com.redberyl.invoiceapp.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;

import jakarta.servlet.http.HttpServletRequest;

/**
 * Controller to handle Single Page Application (SPA) routing
 * This ensures that React Router can handle client-side routing
 * by serving the index.html for all non-API routes
 */
@Controller
public class SpaController {

    /**
     * Serve index.html for all non-API routes when accessed via backend port
     * This allows React Router to handle client-side routing properly
     */
    @GetMapping(value = {
        "/dashboard",
        "/invoices",
        "/invoices/**",
        "/clients",
        "/clients/**",
        "/candidates",
        "/candidates/**",
        "/projects",
        "/projects/**",
        "/payments",
        "/payments/**",
        "/crm",
        "/crm/**",
        "/documents",
        "/documents/**",
        "/bdms",
        "/bdms/**",
        "/redberyl-account",
        "/redberyl-account/**",
        "/hsn-codes",
        "/hsn-codes/**",
        "/staffing-types",
        "/staffing-types/**",
        "/invoice-types",
        "/invoice-types/**",
        "/spocs",
        "/spocs/**"
    })
    public String spa(HttpServletRequest request) {
        // Only handle requests coming to port 8080 (backend)
        // Forward to the static index.html which will redirect to frontend
        return "forward:/index.html";
    }
}

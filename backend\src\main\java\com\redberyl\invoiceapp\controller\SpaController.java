package com.redberyl.invoiceapp.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.servlet.view.RedirectView;

import jakarta.servlet.http.HttpServletRequest;

/**
 * Controller to handle Single Page Application (SPA) routing
 * This ensures that React Router can handle client-side routing
 * by redirecting to the frontend application
 */
@Controller
public class SpaController {

    /**
     * Redirect all non-API routes to the React frontend
     * This allows React Router to handle client-side routing properly
     */
    @GetMapping(value = {
        "/",
        "/dashboard",
        "/invoices",
        "/invoices/**",
        "/clients",
        "/clients/**",
        "/candidates",
        "/candidates/**",
        "/projects",
        "/projects/**",
        "/payments",
        "/payments/**",
        "/crm",
        "/crm/**",
        "/documents",
        "/documents/**",
        "/bdms",
        "/bdms/**",
        "/redberyl-account",
        "/redberyl-account/**",
        "/hsn-codes",
        "/hsn-codes/**",
        "/staffing-types",
        "/staffing-types/**",
        "/invoice-types",
        "/invoice-types/**",
        "/spocs",
        "/spocs/**"
    })
    public RedirectView redirectToFrontend(HttpServletRequest request) {
        // Get the current path and redirect to the same path on the frontend
        String path = request.getRequestURI();
        String frontendUrl = "http://localhost:3000" + path;

        // Add query parameters if they exist
        String queryString = request.getQueryString();
        if (queryString != null && !queryString.isEmpty()) {
            frontendUrl += "?" + queryString;
        }

        return new RedirectView(frontendUrl);
    }
}

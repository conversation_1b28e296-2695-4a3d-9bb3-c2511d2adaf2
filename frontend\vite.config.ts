import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "0.0.0.0", // Listen on all interfaces
    port: 3000,
    strictPort: true,
    open: true,
    cors: true,
    proxy: {
      '/api': {
        target: 'http://localhost:8080', // Try localhost first
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path, // Keep /api prefix for backend endpoints
        // Log more details about the connection
        onProxyReq: (_proxyReq:any, req:any, _res:any) => {
          console.log(`Proxying ${req.method} request to: ${req.url}`);
        },
        onError: (err:any, _req:any, _res:any) => {
          console.error('Proxy error details:', err);
          console.log('Attempting to connect to backend at IP address instead of localhost');

          // Try to connect to the IP address as a fallback
          // This is handled by the error handler and will automatically retry with the IP
        },
        configure: (proxy, _options) => {
          proxy.on('error', (err, req, res) => {
            console.log('proxy error', err);

            // If the proxy fails with localhost, try with the IP address
            // This is a workaround for the proxy error
            if (req && req.url) {
              console.log('Retrying with IP address...');

              // Create a new request to the IP address
              const ipTarget = 'http://************:8080';
              const newPath = req.url.replace('/api', '');
              const newUrl = `${ipTarget}${newPath}`;

              console.log(`Redirecting to: ${newUrl}`);

              // Redirect the request to the IP address
              if (res && !res.headersSent) {
                res.writeHead(307, { 'Location': newUrl });
                res.end();
              }
            }
          });
          proxy.on('proxyReq', (_proxyReq, req, _res) => {
            console.log('Sending Request to the Target:', req.method, req.url);
          });
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            console.log('Received Response from the Target:', proxyRes.statusCode, req.url);
          });
        }
      },
      // Add direct routes for specific endpoints to avoid CORS issues
      '/redberyl-accounts': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path,
      },
      '/candidates': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path,
      },
      '/bdms': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path,
      },
      '/projects': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path,
      },
      '/hsn-codes': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path,
      },
      '/staffing-types': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path,
      },
      '/invoice-types': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path,
      },
      '/invoices': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path,
      }
    }
  },
  base: "/",
  plugins: [
    react(),
    mode === 'development' &&
    componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
}));

package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.ProjectDto;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.service.ProjectService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@Tag(name = "Project", description = "Project management API")
public class ProjectController {

    @Autowired
    private ProjectService projectService;

    @GetMapping("/projects/getAll")
    @Operation(summary = "Get all projects", description = "Get all projects")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Projects found"),
            @ApiResponse(responseCode = "204", description = "No projects found", content = @Content)
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<ProjectDto>> getAllProjects() {
        try {
            List<ProjectDto> projects = projectService.getAllProjects();
            return new ResponseEntity<>(projects, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/projects/getById/{id}")
    @Operation(summary = "Get project by ID", description = "Get project by ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Project found"),
            @ApiResponse(responseCode = "404", description = "Project not found"),
            @ApiResponse(responseCode = "400", description = "Invalid ID supplied")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<ProjectDto> getProjectById(@PathVariable Long id) {
        ProjectDto project = projectService.getProjectById(id);
        return new ResponseEntity<>(project, HttpStatus.OK);
    }

    @GetMapping("/projects/getByClientId/{clientId}")
    @Operation(summary = "Get projects by client ID", description = "Get projects by client ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Projects found"),
            @ApiResponse(responseCode = "204", description = "No projects found for this client"),
            @ApiResponse(responseCode = "404", description = "Client not found"),
            @ApiResponse(responseCode = "400", description = "Invalid client ID supplied")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<ProjectDto>> getProjectsByClientId(@PathVariable Long clientId) {
        try {
            List<ProjectDto> projects = projectService.getProjectsByClientId(clientId);
            return new ResponseEntity<>(projects, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/projects/getByBdmId/{bdmId}")
    @Operation(summary = "Get projects by BDM ID", description = "Get projects by BDM ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Projects found"),
            @ApiResponse(responseCode = "204", description = "No projects found for this BDM"),
            @ApiResponse(responseCode = "404", description = "BDM not found"),
            @ApiResponse(responseCode = "400", description = "Invalid BDM ID supplied")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<ProjectDto>> getProjectsByBdmId(@PathVariable Long bdmId) {
        try {
            List<ProjectDto> projects = projectService.getProjectsByBdmId(bdmId);
            return new ResponseEntity<>(projects, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @PostMapping("/projects/create")
    @Operation(summary = "Create project", description = "Create project. All ID fields support flexible formats:\n\n" +
            "1. Simple string: \"clientId\": \"1\"\n" +
            "2. Simple number: \"clientId\": 1\n" +
            "3. Nested object: \"clientId\": {\"id\": 1, ...}")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Project created successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input or foreign key violation"),
            @ApiResponse(responseCode = "700", description = "Null constraint violation"),
            @ApiResponse(responseCode = "701", description = "Unique constraint violation")
    })
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<ProjectDto> createProject(@Valid @RequestBody ProjectDto projectDto) {
        ProjectDto createdProject = projectService.createProject(projectDto);
        return new ResponseEntity<>(createdProject, HttpStatus.CREATED);
    }

    @PutMapping("/projects/update/{id}")
    @Operation(summary = "Update project", description = "Update project")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Project updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input or foreign key violation"),
            @ApiResponse(responseCode = "404", description = "Project not found"),
            @ApiResponse(responseCode = "700", description = "Null constraint violation"),
            @ApiResponse(responseCode = "701", description = "Unique constraint violation")
    })
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<ProjectDto> updateProject(@PathVariable Long id, @Valid @RequestBody ProjectDto projectDto) {
        ProjectDto updatedProject = projectService.updateProject(id, projectDto);
        return new ResponseEntity<>(updatedProject, HttpStatus.OK);
    }

    @DeleteMapping("/projects/deleteById/{id}")
    @Operation(summary = "Delete project", description = "Delete project")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "Project deleted successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid ID supplied or project is referenced by other entities"),
            @ApiResponse(responseCode = "404", description = "Project not found")
    })
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteProject(@PathVariable Long id) {
        projectService.deleteProject(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}

import React, { useState, useRef } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { format } from "date-fns";
import { Badge } from "@/components/ui/badge";
import { FileText, Download, Printer, ReceiptIcon, Loader2 } from "lucide-react";
import { toast } from "sonner";
import jsPDF from "jspdf";
import html2canvas from "html2canvas";
import { invoiceGenerationService } from "@/services/invoiceGenerationService";

// Flag to track if we're in a browser environment
const isBrowser = typeof window !== 'undefined';

interface InvoiceDetailsProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  invoice: {
    id: string;
    client: string;
    project: string;
    candidate?: string;
    invoiceType?: string;
    staffingType?: string;
    amount: string;
    tax: string;
    total: string;
    issueDate: string;
    dueDate: string;
    status: string;
    recurring: boolean;
    publishedToFinance?: boolean;
    publishedAt?: string;
    hsnCode?: string;
    redberylAccount?: string;
    notes?: string;
  } | null;
  onEdit: (id: string) => void;
}

const InvoiceDetailsDialog: React.FC<InvoiceDetailsProps> = ({
  open,
  onOpenChange,
  invoice,
  onEdit,
}) => {
  const [isDownloading, setIsDownloading] = useState(false);

  // If no invoice is provided, return null
  if (!invoice) {
    console.log("No invoice data provided to InvoiceDetailsDialog");
    return null;
  }

  // Log the invoice data for debugging
  console.log("InvoiceDetailsDialog: Invoice data:", invoice);

  // Ensure all required fields are present
  const safeInvoice = {
    ...invoice,
    client: invoice.client || "Unknown Client",
    project: invoice.project || "Unknown Project",
    candidate: invoice.candidate || "-",
    invoiceType: invoice.invoiceType || "Standard",
    staffingType: invoice.staffingType || "Full-time",
    amount: invoice.amount || "₹0.00",
    tax: invoice.tax || "₹0.00",
    total: invoice.total || "₹0.00",
    issueDate: invoice.issueDate || new Date().toISOString().split('T')[0],
    dueDate: invoice.dueDate || new Date().toISOString().split('T')[0],
    status: invoice.status || "Draft",
    recurring: invoice.recurring || false,
    notes: invoice.notes || ""
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "paid":
        return "bg-green-100 text-green-800 border-green-200";
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "overdue":
        return "bg-red-100 text-red-800 border-red-200";
      case "draft":
        return "bg-gray-100 text-gray-800 border-gray-200";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Reference to the invoice PDF template
  const invoicePdfRef = useRef<HTMLDivElement>(null);

  const handleDownload = async () => {
    setIsDownloading(true);

    try {
      // Make sure we're in a browser environment
      if (!isBrowser) {
        console.error('Cannot generate PDF in non-browser environment');
        toast.error("PDF generation is only available in browser environments");
        setIsDownloading(false);
        return;
      }

      console.log("InvoiceDetailsDialog: Downloading invoice with data:", safeInvoice);

      // Create a temporary div to render the invoice template
      const tempDiv = document.createElement('div');
      tempDiv.style.position = 'absolute';
      tempDiv.style.left = '-9999px';
      tempDiv.style.top = '-9999px';
      tempDiv.style.width = '800px'; // Set a fixed width for better PDF quality
      document.body.appendChild(tempDiv);

      // Import and render the invoice template
      const { default: InvoicePdfTemplate } = await import('./InvoicePdfTemplate');
      const root = document.createElement('div');
      tempDiv.appendChild(root);

      // Use ReactDOM to render the template
      const ReactDOM = await import('react-dom/client');
      const reactRoot = ReactDOM.createRoot(root);
      reactRoot.render(<InvoicePdfTemplate invoice={safeInvoice} />);

      // Wait a moment for the rendering to complete
      await new Promise(resolve => setTimeout(resolve, 100));

      try {
        // Use html2canvas to capture the rendered template
        const canvas = await html2canvas(root, {
          scale: 2, // Higher scale for better quality
          useCORS: true,
          logging: false,
          backgroundColor: '#ffffff'
        });

        // Create a new PDF document
        const pdf = new jsPDF({
          orientation: 'portrait',
          unit: 'mm',
          format: 'a4'
        });

        // Calculate the PDF dimensions
        const imgWidth = 210; // A4 width in mm
        const imgHeight = (canvas.height * imgWidth) / canvas.width;

        // Add the canvas as an image to the PDF
        const imgData = canvas.toDataURL('image/png');
        pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);

        // Save the PDF
        pdf.save(`Invoice-${safeInvoice.id}.pdf`);

        toast.success("Invoice downloaded successfully as PDF");
      } catch (canvasError) {
        console.error("Error generating canvas:", canvasError);
        throw new Error("Failed to generate PDF: " + (canvasError instanceof Error ? canvasError.message : String(canvasError)));
      } finally {
        // Clean up
        document.body.removeChild(tempDiv);
      }
    } catch (error) {
      console.error("Error generating PDF:", error);
      toast.error("Failed to download invoice as PDF. Please try again.");

      // Show more detailed error message
      if (error instanceof Error) {
        console.error("PDF generation error details:", error.message);
        toast.error(`PDF generation error: ${error.message}`);
      }
    } finally {
      setIsDownloading(false);
    }
  };

  const [isPrinting, setIsPrinting] = useState(false);

  const handlePrint = async () => {
    setIsPrinting(true);

    try {
      // Make sure we're in a browser environment
      if (!isBrowser) {
        console.error('Cannot generate PDF for printing in non-browser environment');
        toast.error("PDF generation is only available in browser environments");
        setIsPrinting(false);
        return;
      }

      // Create a temporary div to render the invoice template
      const tempDiv = document.createElement('div');
      tempDiv.style.position = 'absolute';
      tempDiv.style.left = '-9999px';
      tempDiv.style.top = '-9999px';
      tempDiv.style.width = '800px'; // Set a fixed width for better PDF quality
      document.body.appendChild(tempDiv);

      // Import and render the invoice template
      const { default: InvoicePdfTemplate } = await import('./InvoicePdfTemplate');
      const root = document.createElement('div');
      tempDiv.appendChild(root);

      // Use ReactDOM to render the template
      const ReactDOM = await import('react-dom/client');
      const reactRoot = ReactDOM.createRoot(root);
      reactRoot.render(<InvoicePdfTemplate invoice={safeInvoice} />);

      // Wait a moment for the rendering to complete
      await new Promise(resolve => setTimeout(resolve, 100));

      try {
        // Use html2canvas to capture the rendered template
        const canvas = await html2canvas(root, {
          scale: 2, // Higher scale for better quality
          useCORS: true,
          logging: false,
          backgroundColor: '#ffffff'
        });

        // Create a new PDF document
        const pdf = new jsPDF({
          orientation: 'portrait',
          unit: 'mm',
          format: 'a4'
        });

        // Calculate the PDF dimensions
        const imgWidth = 210; // A4 width in mm
        const imgHeight = (canvas.height * imgWidth) / canvas.width;

        // Add the canvas as an image to the PDF
        const imgData = canvas.toDataURL('image/png');
        pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);

        // Get the PDF as a data URL
        const pdfData = pdf.output('datauristring');

        // Open a new window with the PDF for printing
        const printWindow = window.open('');
        if (!printWindow) {
          throw new Error('Pop-up blocked. Please allow pop-ups for this site.');
        }

        // Write the PDF to the new window
        printWindow.document.write(`
          <html>
          <head>
            <title>Print Invoice ${safeInvoice.id}</title>
            <style>
              body, html { margin: 0; padding: 0; height: 100%; }
              iframe { width: 100%; height: 100%; border: none; }
            </style>
          </head>
          <body>
            <iframe src="${pdfData}" width="100%" height="100%"></iframe>
            <script>
              // Auto print when loaded
              window.onload = function() {
                setTimeout(function() {
                  window.print();
                  // Close the window after printing (or after a delay if print is canceled)
                  setTimeout(function() {
                    window.close();
                  }, 1000);
                }, 1000);
              }
            </script>
          </body>
          </html>
        `);

        // Close the document to finish loading
        printWindow.document.close();

        toast.success("Invoice sent to printer");
      } catch (canvasError) {
        console.error("Error generating canvas for print:", canvasError);
        throw new Error("Failed to generate PDF for printing: " + (canvasError instanceof Error ? canvasError.message : String(canvasError)));
      } finally {
        // Clean up
        document.body.removeChild(tempDiv);
      }
    } catch (error) {
      console.error("Error preparing invoice for print:", error);
      toast.error("Failed to print invoice");

      // Show more detailed error message
      if (error instanceof Error) {
        console.error("Print error details:", error.message);
        toast.error(`Print error: ${error.message}`);
      }
    } finally {
      setIsPrinting(false);
    }
  };

  const [isEditing, setIsEditing] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);

  const handleEditInvoice = () => {
    setIsEditing(true);
    try {
      if (onEdit) {
        console.log("InvoiceDetailsDialog: Editing invoice with ID:", safeInvoice.id);
        onEdit(safeInvoice.id);
      }
      onOpenChange(false);
      setIsEditing(false);
    } catch (error) {
      console.error("Error editing invoice:", error);
      toast.error("Failed to edit invoice");
      setIsEditing(false);
    }
  };

  /**
   * Handle generating an invoice PDF using the actual database invoice
   */
  const handleGenerateInvoice = async () => {
    setIsGenerating(true);
    try {
      console.log("InvoiceDetailsDialog: Generating invoice with ID:", safeInvoice.id);

      // First check if backend is available
      try {
        const testResponse = await fetch('/api/invoice-generation/test', {
          method: 'GET',
          headers: { 'Accept': 'text/plain' }
        });

        if (!testResponse.ok) {
          throw new Error('Backend not available');
        }
        console.log("Backend is available, proceeding with invoice generation");
      } catch (error) {
        console.error("Backend not available:", error);
        toast.error("Backend server is not running. Please start the backend server and try again.");
        return;
      }

      // Extract the numeric ID from the invoice ID (e.g., "INV-004" -> 4, "INV-001" -> 1)
      console.log(`DEBUG: Starting ID extraction for: "${safeInvoice.id}"`);

      let numericId;

      // Try different patterns to extract the ID
      const patterns = [
        { name: 'INV-XXX', regex: /^INV-(\d+)$/, group: 1 },
        { name: 'INV-YYYY-XXX', regex: /^INV-\d+-(\d+)$/, group: 1 },
        { name: 'ends with digits', regex: /(\d+)$/, group: 1 }
      ];

      for (const pattern of patterns) {
        const match = safeInvoice.id.match(pattern.regex);
        if (match) {
          numericId = parseInt(match[pattern.group]);
          console.log(`DEBUG: Pattern "${pattern.name}" matched. Extracted: ${numericId}`);
          break;
        } else {
          console.log(`DEBUG: Pattern "${pattern.name}" did not match`);
        }
      }

      // Fallback if no pattern matched
      if (!numericId) {
        const allDigits = safeInvoice.id.replace(/[^0-9]/g, '');
        console.log(`DEBUG: Fallback - all digits extracted: "${allDigits}"`);
        if (allDigits.length >= 3) {
          numericId = parseInt(allDigits.slice(-3));
          console.log(`DEBUG: Taking last 3 digits: ${numericId}`);
        } else if (allDigits.length > 0) {
          numericId = parseInt(allDigits);
          console.log(`DEBUG: Taking all digits: ${numericId}`);
        }
      }

      if (!numericId || isNaN(numericId)) {
        console.error(`DEBUG: Failed to extract valid numeric ID from "${safeInvoice.id}"`);
        toast.error("Invalid invoice ID format");
        return;
      }

      console.log(`DEBUG: Final result - Original ID: "${safeInvoice.id}", Extracted numeric ID: ${numericId}`);

      console.log(`Generating PDF for invoice ID: ${numericId}`);

      // Use Vite proxy to generate PDF directly from the database invoice
      const apiUrls = [
        `/api/invoice-generation/pdf/${numericId}`,
        `/api/invoice-generation/public/pdf/${numericId}`
      ];

      let pdfGenerated = false;
      let lastError = null;

      for (const url of apiUrls) {
        try {
          console.log(`Trying to generate PDF from: ${url}`);

          const response = await fetch(url, {
            method: 'GET',
            headers: {
              'Accept': 'application/pdf',
            }
          });

          if (response.ok) {
            const blob = await response.blob();
            const pdfUrl = URL.createObjectURL(blob);

            // Open the PDF in a new tab
            window.open(pdfUrl, '_blank');

            toast.success("Invoice generated successfully");
            pdfGenerated = true;
            break;
          } else {
            console.log(`Failed to generate PDF from ${url}: ${response.status} ${response.statusText}`);
            lastError = new Error(`HTTP ${response.status}: ${response.statusText}`);
          }
        } catch (error) {
          console.log(`Error with ${url}:`, error);
          lastError = error;
        }
      }

      if (!pdfGenerated) {
        console.error("All PDF generation attempts failed. Last error:", lastError);
        toast.error("Failed to generate invoice PDF. Please check if the backend server is running.");
      }

    } catch (error) {
      console.error("Error generating invoice:", error);
      toast.error("Failed to generate invoice");
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange} modal={true}>
      <DialogContent className="sm:max-w-[600px] bg-white">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>Invoice {safeInvoice.id}</span>
            <div className="flex items-center gap-2">
              {safeInvoice.recurring && (
                <Badge variant="outline" className="bg-blue-50 border-blue-200 text-blue-700">
                  Recurring
                </Badge>
              )}
              <Badge variant="outline" className={getStatusColor(safeInvoice.status)}>
                {safeInvoice.status}
              </Badge>
            </div>
          </DialogTitle>
          <DialogDescription>
            View invoice details and perform actions
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-2 gap-4 py-4">
          <div className="space-y-1">
            <p className="text-sm font-medium text-muted-foreground">Client</p>
            <p className="text-sm font-semibold">{safeInvoice.client}</p>
          </div>
          <div className="space-y-1">
            <p className="text-sm font-medium text-muted-foreground">Project</p>
            <p className="text-sm font-semibold">{safeInvoice.project}</p>
          </div>

          {safeInvoice.candidate && safeInvoice.candidate !== "-" && (
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">Candidate</p>
              <p className="text-sm font-semibold">{safeInvoice.candidate}</p>
            </div>
          )}

          {safeInvoice.invoiceType && (
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">Invoice Type</p>
              <p className="text-sm">{safeInvoice.invoiceType}</p>
            </div>
          )}

          {safeInvoice.staffingType && (
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">Staffing Type</p>
              <p className="text-sm">{safeInvoice.staffingType}</p>
            </div>
          )}

          <div className="space-y-1">
            <p className="text-sm font-medium text-muted-foreground">Issue Date</p>
            <p className="text-sm">{format(new Date(safeInvoice.issueDate), "MMMM d, yyyy")}</p>
          </div>
          <div className="space-y-1">
            <p className="text-sm font-medium text-muted-foreground">Due Date</p>
            <p className="text-sm">{format(new Date(safeInvoice.dueDate), "MMMM d, yyyy")}</p>
          </div>

          <div className="space-y-1">
            <p className="text-sm font-medium text-muted-foreground">Billing Amount</p>
            <p className="text-sm">{safeInvoice.amount}</p>
          </div>
          <div className="space-y-1">
            <p className="text-sm font-medium text-muted-foreground">Tax Amount</p>
            <p className="text-sm">{safeInvoice.tax}</p>
          </div>
          <div className="space-y-1 col-span-2">
            <p className="text-sm font-medium text-muted-foreground">Total Amount</p>
            <p className="text-lg font-bold">{safeInvoice.total}</p>
          </div>

          {safeInvoice.hsnCode && (
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">HSN Code</p>
              <p className="text-sm">{safeInvoice.hsnCode}</p>
            </div>
          )}

          {safeInvoice.redberylAccount && (
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">Redberyl Account</p>
              <p className="text-sm">{safeInvoice.redberylAccount}</p>
            </div>
          )}

          <div className="space-y-1">
            <p className="text-sm font-medium text-muted-foreground">Recurring</p>
            <p className="text-sm">{safeInvoice.recurring ? "Yes" : "No"}</p>
          </div>

          {typeof safeInvoice.publishedToFinance !== 'undefined' && (
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">Published to Finance</p>
              <p className="text-sm">{safeInvoice.publishedToFinance ? "Yes" : "No"}</p>
            </div>
          )}

          {safeInvoice.publishedAt && (
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">Published At</p>
              <p className="text-sm">{format(new Date(safeInvoice.publishedAt), "MMMM d, yyyy HH:mm")}</p>
            </div>
          )}

          {safeInvoice.notes && (
            <div className="space-y-1 col-span-2">
              <p className="text-sm font-medium text-muted-foreground">Notes</p>
              <p className="text-sm">{safeInvoice.notes}</p>
            </div>
          )}
        </div>

        <DialogFooter className="flex justify-between sm:justify-between">
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              className="gap-1"
              onClick={handleDownload}
              disabled={isDownloading}
            >
              {isDownloading ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Downloading...</span>
                </>
              ) : (
                <>
                  <Download className="h-4 w-4" />
                  <span>Download</span>
                </>
              )}
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="gap-1"
              onClick={handlePrint}
              disabled={isPrinting}
            >
              {isPrinting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Printing...</span>
                </>
              ) : (
                <>
                  <Printer className="h-4 w-4" />
                  <span>Print</span>
                </>
              )}
            </Button>
            {safeInvoice.status.toLowerCase() !== "paid" && (
              <Button variant="outline" size="sm" className="gap-1" onClick={() => {
                toast.info("Payment recording functionality will be implemented soon");
              }}>
                <ReceiptIcon className="h-4 w-4" />
                <span>Record Payment</span>
              </Button>
            )}
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={handleGenerateInvoice}
              disabled={isGenerating}
              className="bg-blue-50 hover:bg-blue-100 text-blue-600 hover:text-blue-700 border-blue-200"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Generating...
                </>
              ) : (
                <>
                  <FileText className="h-4 w-4 mr-2" />
                  Generate Invoice
                </>
              )}
            </Button>
            <Button
              onClick={handleEditInvoice}
              disabled={isEditing}
            >
              {isEditing ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Editing...
                </>
              ) : (
                "Edit Invoice"
              )}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default InvoiceDetailsDialog;